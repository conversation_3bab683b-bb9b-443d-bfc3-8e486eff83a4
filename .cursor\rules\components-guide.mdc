---
description:
globs:
alwaysApply: false
---
# Components Guide

The application uses a component-based architecture with Vue 3 and the Composition API.

## Components Organization

- [src/components](mdc:src/components): Reusable components that can be used across multiple views
- [src/views](mdc:src/views): Page components representing routes
- [src/layout](mdc:src/layout): Layout components for the overall application structure

## Global Components

Several components are registered globally in [src/main.ts](mdc:src/main.ts):

- `IconifyIconOffline`: Offline icon component
- `IconifyIconOnline`: Online icon component
- `FontIcon`: Font icon component
- `Auth`: Authorization component for button-level permissions
- `Perms`: Permissions component

## Component Naming Conventions

- Component files use PascalCase (e.g., `ButtonCounter.vue`)
- Components with "Re" prefix are typically extensions or reusable versions of other components

## Component Structure

Components typically follow this structure:

```vue
<script setup lang="ts">
// Imports
import { ref, computed } from 'vue';

// Props
defineProps<{
  propName: string;
}>();

// Emits
const emit = defineEmits<{
  (e: 'update', value: string): void;
}>();

// State and logic
const localState = ref('');

// Computed properties
const computedValue = computed(() => {
  return localState.value.toUpperCase();
});

// Methods
function handleEvent() {
  emit('update', localState.value);
}
</script>

<template>
  <!-- Component template -->
</template>

<style scoped lang="scss">
/* Component styles */
</style>
