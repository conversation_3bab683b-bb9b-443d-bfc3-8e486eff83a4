<template>
  <div class="main">
    <el-row :gutter="20">
      <el-col :span="4" :xs="24">
        <div class="head-container">
          <el-input
            v-model="categoryName"
            placeholder="请输入分类名称"
            clearable
            prefix-icon="Search"
            style="margin-bottom: 20px"
          />
        </div>
        <div class="head-container">
          <el-tree
            ref="categoryTreeRef"
            :data="categoryTreeOptions"
            :props="{ label: 'name', children: 'children' }"
            :expand-on-click-node="false"
            :filter-node-method="filterNode"
            node-key="id"
            highlight-current
            default-expand-all
            @node-click="handleCategoryNodeClick"
          />
        </div>
      </el-col>
      <el-col :span="20" :xs="24">
        <el-form
          ref="formRef"
          :inline="true"
          :model="form"
          class="search-form bg-bg_color w-full pl-8 pt-[12px] overflow-auto"
        >
          <el-form-item label="模型名称" prop="name">
            <el-input
              v-model="form.name"
              placeholder="请输入模型名称"
              clearable
              class="w-[180px]!"
            />
          </el-form-item>
          <el-form-item label="标签" prop="TagKey">
            <el-input
              v-model="form.tagKey"
              placeholder="请输入标签关键词"
              clearable
              class="w-[180px]!"
            />
          </el-form-item>
          <el-form-item>
            <el-button
              type="primary"
              :icon="useRenderIcon('ri:search-line')"
              :loading="loading"
              @click="onSearch"
            >
              搜索
            </el-button>
            <el-button @click="resetForm(formRef)"> 重置 </el-button>
          </el-form-item>
        </el-form>

        <PureTableBar title="模型库" :columns="columns" @refresh="onSearch">
          <template #buttons>
            <el-button type="primary" @click="onAdd"> 新增 </el-button>
            <el-button
              type="danger"
              :disabled="!selectedModels.length"
              @click="handleBatchDelete"
            >
              批量删除
            </el-button>
          </template>
          <template v-slot="{ size, dynamicColumns }">
            <pure-table
              align-whole="center"
              showOverflowTooltip
              table-layout="auto"
              :loading="loading"
              :size="size"
              adaptive
              :adaptiveConfig="{ offsetBottom: 108 }"
              :data="dataList"
              :columns="dynamicColumns"
              :pagination="{ ...pagination, size }"
              :header-cell-style="{
                background: 'var(--el-fill-color-light)',
                color: 'var(--el-text-color-primary)'
              }"
              @selection-change="handleSelectionChange"
              @page-size-change="handleSizeChange"
              @page-current-change="handleCurrentChange"
            >
              <template #formats="{ row }">
                <el-tag
                  v-for="format in row.formats"
                  :key="format.type"
                  class="mr-1 mb-1"
                  type="success"
                >
                  {{ formatModelTypeToLabel(format) }}
                </el-tag>
              </template>
              <template #previewImage="{ row }">
                <div class="preview-image-container">
                  <el-image
                    v-if="row.previewImage"
                    :src="row.previewImage"
                    :preview-src-list="[row.previewImage]"
                    fit="cover"
                    style="width: 60px; height: 60px; border-radius: 4px"
                    :preview-teleported="true"
                  />
                  <div
                    v-else
                    class="no-preview-placeholder"
                    style="
                      width: 60px;
                      height: 60px;
                      border: 1px dashed #dcdfe6;
                      border-radius: 4px;
                      display: flex;
                      align-items: center;
                      justify-content: center;
                      color: #909399;
                      font-size: 12px;
                      text-align: center;
                      cursor: pointer;
                    "
                    @click="handleUpload(row)"
                  >
                    <div>
                      <el-icon style="font-size: 16px; margin-bottom: 4px">
                        <Plus />
                      </el-icon>
                      <div>上传模型</div>
                    </div>
                  </div>
                </div>
              </template>
              <template #tags="{ row }">
                <el-tag
                  v-for="tag in row.tag?.split(',')"
                  :key="tag"
                  class="mr-1"
                  type="info"
                  effect="light"
                  size="small"
                >
                  {{ tag }}
                </el-tag>
              </template>
              <template #operation="{ row }">
                <el-button link type="primary" @click="handleUpload(row)">
                  上传
                </el-button>
                <el-button
                  class="reset-margin mr-2"
                  link
                  type="primary"
                  :size="size"
                  @click="handleDownload(row)"
                >
                  下载
                </el-button>
                <el-button link type="primary" @click="handleEdit(row)">
                  编辑
                </el-button>
                <el-button link type="primary" @click="handleDelete(row)">
                  删除
                </el-button>
              </template>
            </pure-table>
          </template>
        </PureTableBar>
        <ModelDialog
          v-if="modelDialogVisible"
          v-model:visible="modelDialogVisible"
          :edit-model="editModel"
          @submit-success="onSearch"
        />
        <UploadDialog
          v-if="uploadDialogVisible"
          v-model:visible="uploadDialogVisible"
          :model-data="currentModel"
          :temp-model-info="tempModelData"
          @submit-success="onSearch"
        />
        <DownloadDialog
          v-if="downloadDialogVisible"
          v-model="downloadDialogVisible"
          :model-data="currentModel"
          @update-downloads="handleUpdateDownloads"
        />
        <FormatDialog
          v-model:visible="formatDialogVisible"
          :model-data="currentModel"
          @submit-success="onSearch"
        />
      </el-col>
    </el-row>
  </div>
</template>
<script setup lang="tsx">
import categoryService from "@/api/category";
import libraryService from "@/api/model";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import { PureTableBar } from "@/components/RePureTableBar";
import type { Category, ModelLibrary } from "@/modules";
import { message } from "@/utils/message";
import type { PaginationProps } from "@pureadmin/table";
import { ElMessageBox, ElTree } from "element-plus";
import { Plus } from "@element-plus/icons-vue";
import { onMounted, reactive, ref, watch } from "vue";
import { useRouter } from "vue-router";
import { handleTree } from "@/utils/tree";
import DownloadDialog from "../model-library/components/download-dialog.vue";
import FormatDialog from "../model-library/components/format-dialog.vue";
import ModelDialog from "../model-library/components/model-dialog.vue";
import UploadDialog from "../model-library/components/upload-dialog.vue";

import { formatModelTypeToLabel } from "@/utils/fileUtils";

defineOptions({
  name: "ModelManage"
});

const formRef = ref();
const categoryTreeRef = ref<InstanceType<typeof ElTree>>();
const categoryName = ref("");
const categoryTreeOptions = ref<Category[]>([]);

const form = reactive({
  name: "",
  category: "",
  lodLevel: "",
  tagKey: "",
  categoryId: null
});
const loading = ref(true);
const dataList = ref([]);
const pagination = reactive<PaginationProps>({
  total: 0,
  pageSize: 10,
  currentPage: 1,
  background: true
});
const categories = ref<Category[]>([]);
const categoriesLoading = ref(false);

const columns: TableColumnList = [
  {
    type: "selection"
  },
  {
    label: "模型名称",
    prop: "name",
    minWidth: 120
  },
  {
    label: "预览图",
    prop: "previewImage",
    minWidth: 100,
    slot: "previewImage"
  },
  {
    label: "模型分类",
    prop: "category",
    formatter: (row: ModelLibrary) => {
      return row.modelCategory?.name || "--";
    },
    minWidth: 100
  },
  {
    label: "标签",
    prop: "tags",
    minWidth: 150,
    slot: "tags"
  },
  {
    label: "可用格式",
    prop: "formats",
    minWidth: 150,
    slot: "formats"
  },
  {
    label: "总下载次数",
    prop: "totalDownCount",
    minWidth: 100
  },
  {
    label: "更新时间",
    prop: "modifyTime",
    minWidth: 140
  },
  {
    label: "操作",
    fixed: "right",
    width: 250,
    slot: "operation"
  }
];

const modelDialogVisible = ref(false);
const uploadDialogVisible = ref(false);
const downloadDialogVisible = ref(false);
const previewDialogVisible = ref(false);
const formatDialogVisible = ref(false);
const currentModel = ref(null);
const editModel = ref(null);
const tempModelData = ref(null);
const selectedModels = ref([]);

const fetchCategories = async () => {
  categoriesLoading.value = true;
  try {
    const result = await categoryService.getAllCategories();
    const allCategories = result.data || [];
    categories.value = allCategories;
    categoryTreeOptions.value = handleTree(allCategories);
  } catch (error) {
    console.error("获取分类列表失败:", error);
    message("获取分类列表失败", { type: "error" });
  } finally {
    categoriesLoading.value = false;
  }
};

const fetchData = async () => {
  loading.value = true;
  try {
    const params = {
      pageIndex: pagination.currentPage,
      pageSize: pagination.pageSize,
      name: form.name,
      modelCategoryId: form.categoryId ? Number(form.categoryId) : undefined,
      tagKey: form.tagKey
    };

    const result = await libraryService.getList(params);
    dataList.value = result.data || [];
    pagination.total = result.totalCount || 0;
  } catch (error) {
    console.error("获取数据失败:", error);
    message("获取数据失败", { type: "error" });
  } finally {
    loading.value = false;
  }
};

const onSearch = async () => {
  pagination.currentPage = 1;
  await fetchData();
};

const resetForm = formEl => {
  if (!formEl) return;
  formEl.resetFields();
  onSearch();
};

const handleCurrentChange = val => {
  pagination.currentPage = val;
  fetchData();
};

const handleSizeChange = val => {
  pagination.pageSize = val;
  fetchData();
};

const handleSelectionChange = val => {
  selectedModels.value = val;
};

const onAdd = () => {
  editModel.value = null;
  modelDialogVisible.value = true;
};

const handleEdit = row => {
  editModel.value = row;
  modelDialogVisible.value = true;
};

const handleUpload = row => {
  currentModel.value = row;
  uploadDialogVisible.value = true;
};

const handleDownload = row => {
  currentModel.value = row;
  downloadDialogVisible.value = true;
};

const handleDelete = row => {
  ElMessageBox.confirm("确认删除该模型?", "提示", {
    confirmButtonText: "确认",
    cancelButtonText: "取消",
    type: "warning"
  }).then(async () => {
    try {
      await libraryService.delete(row.id);
      message(`删除成功: ${row.name}`, { type: "success" });
      fetchData();
    } catch (error) {
      console.error("删除失败:", error);
      message("删除失败", { type: "error" });
    }
  });
};

const handleBatchDelete = () => {
  if (selectedModels.value.length === 0) return;

  ElMessageBox.confirm(
    `确认删除选中的 ${selectedModels.value.length} 个模型?`,
    "提示",
    {
      confirmButtonText: "确认",
      cancelButtonText: "取消",
      type: "warning"
    }
  ).then(async () => {
    try {
      const promises = selectedModels.value.map(model =>
        libraryService.delete(model.id)
      );
      await Promise.all(promises);

      message(`批量删除成功: ${selectedModels.value.length} 个模型`, {
        type: "success"
      });
      fetchData();
    } catch (error) {
      console.error("批量删除失败:", error);
      message("批量删除失败", { type: "error" });
    }
  });
};

const handleUpdateDownloads = () => {
  message("下载次数已更新", { type: "success" });
  fetchData();
};

// 分类树相关函数
const filterNode = (value: string, data: Category) => {
  if (!value) return true;
  return data.name.indexOf(value) !== -1;
};

const handleCategoryNodeClick = (data: Category) => {
  if (data.id === form.categoryId) {
    categoryTreeRef.value!.setCurrentKey(null);
    form.categoryId = null;
  } else {
    form.categoryId = data.id;
  }
  onSearch();
};

watch(categoryName, val => {
  categoryTreeRef.value!.filter(val);
});

onMounted(async () => {
  await fetchCategories();
  fetchData();
});
</script>

<style lang="scss" scoped>
.preview-image-container {
  display: flex;
  align-items: center;
  justify-content: center;
}

.no-preview-placeholder {
  transition: all 0.3s ease;

  &:hover {
    border-color: #409eff;
    color: #409eff;
    background-color: #f0f9ff;
  }
}
</style>
