---
description:
globs:
alwaysApply: false
---
# Project Overview

This is a Vue 3 admin template based on the pure-admin system. It's built with TypeScript, Vue 3, Vite, Pinia, and Element Plus.

## Key Entry Points

- [src/main.ts](mdc:src/main.ts): Main application entry point
- [src/App.vue](mdc:src/App.vue): Root component
- [src/router/index.ts](mdc:src/router/index.ts): Router configuration
- [vite.config.ts](mdc:vite.config.ts): Vite build configuration

## Project Structure

- `/src/api`: API request definitions
- `/src/assets`: Static assets like images and icons
- `/src/components`: Reusable Vue components
- `/src/config`: Application configuration 
- `/src/directives`: Custom Vue directives
- `/src/hooks`: Custom Vue composition hooks
- `/src/layout`: Layout components
- `/src/modules`: Feature modules
- `/src/plugins`: Plugin configurations
- `/src/router`: Routing definitions
- `/src/store`: Pinia state management
- `/src/style`: Global styles
- `/src/utils`: Utility functions
- `/src/views`: Page components

## Basic Tech Stack

- Vue 3
- TypeScript
- Vite
- Pinia (State Management)
- Element Plus (UI Components)
- Tailwind CSS
