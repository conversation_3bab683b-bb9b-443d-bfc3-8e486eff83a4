<template>
  <el-dialog
    v-model="visible"
    :title="editModel ? '编辑模型' : '新增模型'"
    width="60%"
  >
    <el-form ref="formRef" :model="form" label-width="120px" :rules="rules">
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="模型名称" prop="name">
            <el-input
              v-model="form.name"
              placeholder="请输入模型名称"
              clearable
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="模型分类" prop="modelCategoryId">
            <el-tree-select
              v-model="form.modelCategoryId"
              :data="categoryTreeOptions"
              :props="{ value: 'id', label: 'name', children: 'children' }"
              placeholder="请选择模型分类"
              clearable
              check-strictly
              :render-after-expand="false"
              :loading="categoriesLoading"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="标签" prop="tags">
            <el-select
              v-model="form.tags"
              multiple
              filterable
              allow-create
              default-first-option
              placeholder="请输入标签，可自定义"
              style="width: 100%"
            >
              <el-option label="高精度" value="高精度" />
              <el-option label="低面数" value="低面数" />
              <el-option label="PBR材质" value="PBR材质" />
              <el-option label="动画" value="动画" />
              <el-option label="真实渲染" value="真实渲染" />
              <el-option label="卡通风格" value="卡通风格" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="visible = false">取消</el-button>
        <el-button type="primary" :loading="loading" @click="handleSubmit">
          {{ "确定" }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, watch, onMounted } from "vue";
import type { FormInstance } from "element-plus";
import { message } from "@/utils/message";
import libraryService from "@/api/model";
import categoryService from "@/api/category";
import type { Category, ModelLibrary } from "@/modules";
import { handleTree } from "@/utils/tree";

defineOptions({
  name: "ModelDialog"
});

const props = defineProps<{
  editModel?: ModelLibrary;
}>();

const emits = defineEmits(["submit-success", "next-step"]);
const visible = defineModel("visible", { default: false, required: true });
const formRef = ref<FormInstance>();
const loading = ref(false);
const categoriesLoading = ref(false);
const categories = ref<Category[]>([]);
const categoryTreeOptions = ref<Category[]>([]);

const rules = {
  name: [
    { required: true, message: "请输入模型名称", trigger: "blur" },
    { min: 2, max: 50, message: "长度在2到50个字符之间", trigger: "blur" }
  ],
  modelCategoryId: [
    { required: true, message: "请选择模型分类", trigger: "change" }
  ]
};

const form = ref<ModelLibrary>({
  name: "",
  modelCategoryId: null,
  tags: [] as string[]
});

const fetchCategories = async () => {
  categoriesLoading.value = true;
  try {
    const result = await categoryService.getAllCategories();
    const allCategories = result.data || [];
    categories.value = allCategories;
    categoryTreeOptions.value = handleTree(allCategories);
  } catch (error) {
    console.error("获取分类列表失败:", error);
    message("获取分类列表失败", { type: "error" });
  } finally {
    categoriesLoading.value = false;
  }
};

watch(
  () => props.editModel,
  newVal => {
    if (newVal) {
      form.value.name = newVal.name || "";
      form.value.modelCategoryId = newVal.modelCategoryId;
      form.value.tags = newVal.tag && newVal.tag.split(",");
    }
  },
  { immediate: true }
);

// 监听对话框显示状态
watch(
  () => visible.value,
  newVal => {
    if (newVal && categories.value.length === 0) {
      fetchCategories();
    }
  }
);

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return;

  await formRef.value.validate(async (valid, fields) => {
    if (!valid) {
      return;
    }

    loading.value = true;

    try {
      const modelData = {
        name: form.value.name,
        tag: form.value.tags.join(","),
        modelCategoryId: form.value.modelCategoryId
      };

      let result;
      if (props.editModel) {
        // 编辑模式
        result = await libraryService.update({
          ...modelData,
          id: props.editModel.id
        });
      } else {
        // 新增模式
        result = await libraryService.add(modelData);
      }

      message(`${props.editModel ? "编辑" : "新增"}成功`, { type: "success" });
      visible.value = false;
      emits("submit-success");
    } catch (error) {
      console.error("提交失败:", error);
      message(`${props.editModel ? "编辑" : "新增"}失败`, { type: "error" });
    } finally {
      loading.value = false;
    }
  });
};

onMounted(() => {
  fetchCategories();
});
</script>

<style lang="scss" scoped></style>
