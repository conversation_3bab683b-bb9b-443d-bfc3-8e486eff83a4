import type { FileModel, ModelFileLog } from "@/modules";
import { http } from "@/utils/http";
import type { ListQuery, ListResult, Result } from "@/utils/models";

class FileModelService {
  private fileModelUrl = "/api/modelFile";
  getAllByLibraryId(modelLibraryId: number) {
    return http.get<ListResult<FileModel>>(
      `${this.fileModelUrl}/GetListByModelLibraryId`,
      {
        params: { modelLibraryId }
      }
    );
  }

  add(data: FileModel) {
    return http.post<FileModel, Result<number>>(`${this.fileModelUrl}`, {
      data
    });
  }

  update(data: FileModel) {
    return http.put<FileModel, ListResult<FileModel>>(
      `${this.fileModelUrl}`,
      data
    );
  }

  delete(id: number) {
    return http.delete<Result<number>>(`${this.fileModelUrl}`, id);
  }

  download(id: number) {
    return http.get<Blob>(`${this.fileModelUrl}/DownFile/${id}`, {
      responseType: "blob",
      beforeResponseCallback: response => {
        const contentDisposition = response.headers["content-disposition"];
        const fileNameMatch =
          contentDisposition && contentDisposition.match(/filename=([^;]+)/);
        const fileName = fileNameMatch
          ? decodeURIComponent(fileNameMatch[1])
          : `file_${id}`;

        const url = window.URL.createObjectURL(new Blob([response.data]));
        const link = document.createElement("a");
        link.href = url;
        link.setAttribute("download", fileName);
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        window.URL.revokeObjectURL(url);

        return response;
      }
    });
  }

  modelFileLog(query: ListQuery) {
    return http.get<ListResult<ModelFileLog>>(
      `${this.fileModelUrl}/getModelFileLogByPage`,
      {
        params: {
          pageSize: query.pageSize,
          pageIndex: query.pageIndex,
          ...query.query
        }
      }
    );
  }
}

export default new FileModelService();
