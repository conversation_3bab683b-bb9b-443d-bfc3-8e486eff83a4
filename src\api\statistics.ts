import { http } from "@/utils/http";
import type { Result, GetResultList } from "@/utils/models";
class StatisticsService {
  private baseUrl = "/api/Statistics";

  public getModelLibraryDown<T = DataD>(
    payload: Record<string, any>
  ): Promise<GetResultList<T>> {
    return http.post<Record<string, any>, GetResultList<T>>(
      `${this.baseUrl}/GetModelLibraryDownAsync`,
      {
        data: payload
      }
    );
  }

  public getModelLibraryDownTop10<T = DataD>(): Promise<GetResultList<T>> {
    return http.get<null, GetResultList<T>>( // no params
      `${this.baseUrl}/GetModelLibraryDownTop10Async`
    );
  }

  public getModelCategoryDown<T = DataD>(): Promise<GetResultList<T>> {
    return http.get<null, GetResultList<T>>( // no params
      `${this.baseUrl}/GetModelCategoryDownAsync`
    );
  }

  public getModelCategoryLibraryCount<T = DataD>(): Promise<GetResultList<T>> {
    return http.get<null, GetResultList<T>>( // no params
      `${this.baseUrl}/GetModelCategoryLibraryCountAsync`
    );
  }

  public getModelFormatDown<T = DataD>(
    payload: Record<string, any>
  ): Promise<Result<T[]>> {
    return http.get<Record<string, any>, Result<T[]>>(
      `${this.baseUrl}/GetModelForamtDownAsync`,
      {
        params: payload
      }
    );
  }

  public getModelFormatUploadCount<T = DataD>(): Promise<GetResultList<T>> {
    return http.get<Record<string, any>, GetResultList<T>>(
      `${this.baseUrl}/GetModelForamtUploadCountAsync`
    );
  }
}

const statisticsService = new StatisticsService();
export default statisticsService;

export interface DataD {
  y: number;
  x: string;
}
