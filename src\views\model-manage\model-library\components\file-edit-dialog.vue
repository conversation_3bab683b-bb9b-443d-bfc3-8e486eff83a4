<template>
  <el-dialog v-model="visible" title="编辑文件信息" width="50%">
    <el-form ref="formRef" :model="form" label-width="120px" :rules="rules">
      <el-form-item label="文件名">
        <el-input v-model="fileInfo.name" disabled />
      </el-form-item>

      <el-form-item label="文件格式">
        <el-input v-model="formatLabel" disabled />
      </el-form-item>

      <el-form-item label="文件大小">
        <el-input v-model="fileInfo.fileSize" disabled />
      </el-form-item>

      <el-form-item label="预览图片">
        <div class="preview-upload-container">
          <el-upload
            class="preview-uploader"
            action="#"
            :show-file-list="false"
            :before-upload="handlePreviewUpload"
            accept="image/*"
          >
            <img
              v-if="previewImageUrl"
              :src="previewImageUrl"
              class="preview-image"
            />
            <el-icon v-else class="preview-uploader-icon"><Plus /></el-icon>
          </el-upload>
          <div v-if="previewImageUrl" class="preview-actions">
            <el-button size="small" @click="removePreviewImage"
              >移除图片</el-button
            >
          </div>
        </div>
      </el-form-item>

      <el-form-item label="LOD等级" prop="lodLevel">
        <el-input
          v-model="form.lodLevel"
          placeholder="请输入LOD等级"
          clearable
        />
      </el-form-item>

      <el-form-item label="备注" prop="describe">
        <el-input
          v-model="form.describe"
          type="textarea"
          placeholder="请输入备注信息"
          :rows="4"
          clearable
        />
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="visible = false">取消</el-button>
        <el-button type="primary" :loading="loading" @click="handleSubmit">
          保存
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, watch, computed } from "vue";
import type { FormInstance, UploadRawFile } from "element-plus";
import { Plus } from "@element-plus/icons-vue";
import { message } from "@/utils/message";
import FileModelService from "@/api/file";
import OSSService from "@/api/oss";
import type { FileModel } from "@/modules";
import { formatModelTypeToLabel } from "@/utils/fileUtils";

defineOptions({
  name: "FileEditDialog"
});

const props = defineProps<{
  fileData?: FileModel;
  visible?: boolean;
}>();

const emits = defineEmits(["submit-success", "update:visible"]);

const visible = computed({
  get: () => props.visible || false,
  set: (value: boolean) => emits("update:visible", value)
});
const formRef = ref<FormInstance>();
const loading = ref(false);

const form = ref({
  lodLevel: "",
  describe: "",
  previewImage: ""
});

const fileInfo = ref({
  name: "",
  fileSize: "",
  format: undefined
});

const previewImageUrl = ref("");

// 计算格式显示标签
const formatLabel = computed(() => {
  if (fileInfo.value.format !== undefined) {
    return formatModelTypeToLabel(fileInfo.value.format);
  }
  return "未知格式";
});

// 表单验证规则
const rules = {
  lodLevel: [{ max: 50, message: "LOD等级不能超过50个字符", trigger: "blur" }],
  describe: [{ max: 500, message: "备注不能超过500个字符", trigger: "blur" }]
};

// 监听对话框显示状态，初始化表单数据
watch(
  () => visible.value,
  newValue => {
    console.log("FileEditDialog - visible changed:", newValue);
    console.log("FileEditDialog - props.fileData:", props.fileData);
    if (newValue && props.fileData) {
      console.log(
        "FileEditDialog - initializing form with data:",
        props.fileData
      );
      // 初始化表单数据
      form.value = {
        lodLevel: props.fileData.lodLevel || "",
        describe: props.fileData.describe || "",
        previewImage: props.fileData.previewImage || ""
      };

      // 初始化预览图片
      previewImageUrl.value = props.fileData.previewImage || "";

      // 初始化文件信息显示
      fileInfo.value = {
        name: extractFileName(props.fileData.address || ""),
        fileSize: props.fileData.fileSize || "",
        format: props.fileData.format
      };

      console.log("FileEditDialog - form initialized:", form.value);
      console.log("FileEditDialog - fileInfo initialized:", fileInfo.value);

      // 清除表单验证状态
      if (formRef.value) {
        formRef.value.clearValidate();
      }
    }
  }
);

// 从文件地址中提取文件名
const extractFileName = (address: string): string => {
  if (!address) return "未知文件";
  const parts = address.split("/");
  return parts[parts.length - 1] || "未知文件";
};

// 处理预览图片上传
const handlePreviewUpload = async (rawFile: UploadRawFile) => {
  try {
    // 创建本地预览URL
    previewImageUrl.value = URL.createObjectURL(rawFile);

    // 上传到OSS
    const result = await OSSService.upload(rawFile);
    if (result && result.length > 0) {
      form.value.previewImage = result[0].filePath;
      message("预览图片上传成功", { type: "success" });
    }
  } catch (error) {
    message("预览图片上传失败", { type: "error" });
    console.error(error);
  }

  // 阻止默认上传行为
  return false;
};

// 移除预览图片
const removePreviewImage = () => {
  previewImageUrl.value = "";
  form.value.previewImage = "";
  message("预览图片已移除", { type: "info" });
};

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value || !props.fileData) return;

  await formRef.value.validate(async valid => {
    if (!valid) {
      return;
    }

    loading.value = true;
    try {
      const updateData: FileModel = {
        ...props.fileData,
        lodLevel: form.value.lodLevel,
        describe: form.value.describe,
        previewImage: form.value.previewImage
      };

      await FileModelService.update(updateData);
      message("文件信息更新成功", { type: "success" });
      visible.value = false;
      emits("submit-success");
    } catch (error) {
      console.error("更新失败:", error);
      message("更新失败", { type: "error" });
    } finally {
      loading.value = false;
    }
  });
};
</script>

<style lang="scss" scoped>
.dialog-footer {
  text-align: right;
}

:deep(.el-form-item__label) {
  font-weight: 500;
}

:deep(.el-input.is-disabled .el-input__inner) {
  background-color: #f5f7fa;
  border-color: #e4e7ed;
  color: #606266;
}

.preview-upload-container {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.preview-uploader {
  :deep(.el-upload) {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    transition: all 0.3s;
    width: 120px;
    height: 120px;
    display: flex;
    align-items: center;
    justify-content: center;

    &:hover {
      border-color: #409eff;
    }
  }
}

.preview-image {
  width: 120px;
  height: 120px;
  object-fit: cover;
  border-radius: 6px;
}

.preview-uploader-icon {
  font-size: 28px;
  color: #8c939d;
}

.preview-actions {
  display: flex;
  gap: 8px;
}
</style>
