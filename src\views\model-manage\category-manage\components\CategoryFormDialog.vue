<template>
  <el-dialog
    v-model="isVisible"
    :title="isEdit ? '编辑分类' : '新增分类'"
    width="500px"
    @close="closeDialog"
  >
    <el-form ref="formRef" :model="form" label-width="80px" :rules="rules">
      <el-form-item label="父级分类" prop="parentId">
        <el-tree-select
          v-model="form.parentId"
          :data="categoryOptions"
          :props="{ value: 'id', label: 'name', children: 'children' }"
          placeholder="请选择父级分类"
          clearable
          check-strictly
          :render-after-expand="false"
          style="width: 100%"
        />
      </el-form-item>
      <el-form-item label="名称" prop="name">
        <el-input v-model="form.name" placeholder="请输入分类名称" />
      </el-form-item>
      <el-form-item label="分类码" prop="code">
        <el-input v-model="form.code" placeholder="请输入分类代码" />
      </el-form-item>
      <el-form-item label="排序" prop="sort">
        <el-input-number v-model="form.sort" :min="0" :max="999" />
      </el-form-item>
      <el-form-item label="描述" prop="describe">
        <el-input
          v-model="form.describe"
          type="textarea"
          :rows="3"
          placeholder="请输入分类描述"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="closeDialog">取消</el-button>
        <el-button type="primary" @click="submitForm">确定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, watch } from "vue";
import type { FormInstance, FormRules } from "element-plus";
import { message } from "@/utils/message";
import categoryService from "@/api/category";
import { handleTree } from "@/utils/tree";
import type { Category } from "@/modules";

const props = defineProps({
  isEdit: {
    type: Boolean,
    default: false
  },
  categoryData: {
    type: Object,
    default: () => null
  },
  parentId: {
    type: Number,
    default: null
  }
});

const emit = defineEmits(["submitSuccess"]);

const isVisible = defineModel<boolean>("modelValue");
const formRef = ref<FormInstance>();
const categoryOptions = ref<Category[]>([]);

const form = reactive({
  id: null,
  name: "",
  code: "",
  sort: 0,
  describe: "",
  status: 1,
  parentId: null
});

const rules = reactive<FormRules>({
  name: [
    { required: true, message: "请输入分类名称", trigger: "blur" },
    { min: 2, max: 20, message: "长度在2到20个字符之间", trigger: "blur" }
  ],
  code: [
    { required: true, message: "请输入分类代码", trigger: "blur" },
    {
      pattern: /^[a-z0-9-]+$/,
      message: "只能包含小写字母、数字和连字符",
      trigger: "blur"
    }
  ]
});

function resetForm() {
  form.id = null;
  form.name = "";
  form.code = "";
  form.sort = 0;
  form.describe = "";
  form.parentId = null;
}

function closeDialog() {
  resetForm();
  isVisible.value = false;
}

async function submitForm() {
  if (!formRef.value) return;
  try {
    await formRef.value.validate();
    const dataToSend = { ...form };

    if (props.isEdit) {
      await categoryService.update(dataToSend);
      message("编辑成功", { type: "success" });
    } else {
      await categoryService.add(dataToSend);
      message("添加成功", { type: "success" });
    }
    emit("submitSuccess");
    closeDialog();
  } catch (error) {
    console.error("Category form submission failed:", error);
    message("操作失败，请稍后重试", { type: "error" });
  }
}

async function fetchCategories() {
  try {
    const res = await categoryService.getAllCategories();
    const allCategories = res.data || [];

    // 如果是编辑模式，需要过滤掉当前分类及其子分类，避免循环引用
    let filteredCategories = allCategories;
    if (props.isEdit && props.categoryData) {
      filteredCategories = filterCurrentAndChildren(
        allCategories,
        props.categoryData.id
      );
    }

    categoryOptions.value = handleTree(filteredCategories);
  } catch (error) {
    console.error("获取分类列表失败:", error);
    message("获取分类列表失败", { type: "error" });
  }
}

function filterCurrentAndChildren(
  categories: Category[],
  currentId: number
): Category[] {
  return categories.filter(category => {
    if (category.id === currentId) return false;
    // 这里可以添加更复杂的逻辑来过滤子分类
    return true;
  });
}

function initFormData() {
  if (props.isEdit && props.categoryData) {
    form.id = props.categoryData.id;
    form.name = props.categoryData.name;
    form.code = props.categoryData.code;
    form.sort = props.categoryData.sort;
    form.describe = props.categoryData.describe;
    // 如果 parentId 为 0 或 null 或 undefined，则设置为 null
    form.parentId =
      props.categoryData.parentId && props.categoryData.parentId !== 0
        ? props.categoryData.parentId
        : null;
  } else if (props.parentId) {
    form.parentId = props.parentId;
  }
}

watch(
  () => props.categoryData,
  () => {
    initFormData();
  },
  { immediate: true }
);

watch(
  () => props.parentId,
  newParentId => {
    if (!props.isEdit) {
      // 确保 parentId 为 0 时设置为 null
      form.parentId = newParentId && newParentId !== 0 ? newParentId : null;
    }
  },
  { immediate: true }
);

onMounted(() => {
  fetchCategories();
  initFormData();
});
</script>

<style lang="scss" scoped>
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}
</style>
