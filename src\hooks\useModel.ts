import { ref, reactive, onMounted } from "vue";
import { message } from "@/utils/message";

export const useModel = () => {
  const loading = ref(true);
  const dataList = ref([]);

  // 处理搜索
  const handleSearch = async (params = {}) => {
    try {
      loading.value = true;
      // 实际项目中应该使用API请求来获取数据
      // const { data } = await getModelList(params);
      // dataList.value = data.list;

      // 模拟数据
      setTimeout(() => {
        dataList.value = [
          {
            id: 1,
            name: "现代办公楼模型",
            category: "建筑",
            originalType: "obj",
            result: "处理成功",
            createTime: "2024-08-01 09:23:45"
          },
          {
            id: 2,
            name: "欧式沙发组合",
            category: "家具",
            originalType: "glb",
            result: "处理成功",
            createTime: "2024-08-02 14:12:30"
          },
          {
            id: 3,
            name: "电动汽车概念设计",
            category: "交通",
            originalType: "gltf",
            result: "处理中",
            createTime: "2024-08-03 11:45:22"
          }
        ];
        loading.value = false;
      }, 500);
    } catch (error) {
      console.error(error);
      loading.value = false;
    }
  };

  // 获取模型统计数据
  const getModelStatistics = async () => {
    try {
      // 实际项目中应该使用API请求来获取数据
      // const { data } = await getModelStats();
      // return data;

      // 模拟数据
      return {
        totalModels: 32,
        processedModels: 24,
        processingModels: 5,
        failedModels: 3
      };
    } catch (error) {
      console.error(error);
      return {
        totalModels: 0,
        processedModels: 0,
        processingModels: 0,
        failedModels: 0
      };
    }
  };

  // 处理重置
  const handleReset = formRef => {
    if (!formRef) return;
    formRef.resetFields();
    handleSearch();
  };

  // 删除模型
  const handleDel = async row => {
    try {
      // 实际项目中应该使用API请求
      // await deleteModel(row.id);

      message(`删除成功：${row.name}`, { type: "success" });
      handleSearch();
    } catch (error) {
      console.error(error);
    }
  };

  // 更改模型状态
  const switchStatus = async row => {
    try {
      message(`状态已更新：${row.name}`, { type: "success" });
    } catch (error) {
      console.error(error);
    }
  };

  // 处理模型
  const handleProcess = async rows => {
    try {
      const ids = Array.isArray(rows) ? rows.map(row => row.id) : [rows.id];
      // 实际项目中应该使用API请求
      // await batchProcessModels(ids);

      message("处理任务已提交", { type: "success" });
      handleSearch();
    } catch (error) {
      console.error(error);
    }
  };

  onMounted(() => {
    handleSearch();
  });

  return {
    loading,
    dataList,
    switchStatus,
    handleSearch,
    handleReset,
    handleDel,
    handleProcess,
    getModelStatistics
  };
};
