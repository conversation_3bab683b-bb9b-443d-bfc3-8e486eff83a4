<template>
  <el-dialog
    v-model="visible"
    :title="
      modelData ? '上传模型文件' : tempModelInfo ? '上传模型文件' : '上传新模型'
    "
    width="60%"
    top="2vh"
  >
    <div v-if="modelData">
      <div class="model-info">
        <h3>{{ modelData.name }}</h3>
        <p>模型分类: {{ modelData.modelCategory?.name }}</p>

        <el-divider content-position="center" />
        <el-form label-width="80px">
          <el-form-item label="LOD等级" prop="lodLevel">
            <el-input v-model="form.lodLevel" placeholder="请输入LOD等级" />
          </el-form-item>

          <el-form-item label="备注" prop="describe">
            <el-input v-model="form.describe" placeholder="请输入备注" />
          </el-form-item>
        </el-form>
      </div>

      <el-divider content-position="center">上传新格式</el-divider>
    </div>

    <div v-else-if="tempModelInfo" class="model-info">
      <h3>{{ tempModelInfo.name }}</h3>
      <p>模型分类: {{ tempModelInfo.category }}</p>
      <p>LOD等级: {{ tempModelInfo.lodLevel }}</p>
      <el-divider content-position="center">上传模型文件</el-divider>
    </div>

    <el-form ref="formRef" :model="form" label-width="120px" :rules="rules">
      <el-form-item label="模型附件">
        <div class="upload-container">
          <div
            class="upload-box"
            @click="handleUploadClick"
            @drop.prevent="handleDrop"
            @dragover.prevent
          >
            <div class="upload-content">
              <el-icon class="upload-icon"><Upload /></el-icon>
              <div class="upload-text">
                点击上传图模文件，或拖拽图模文件至框内
              </div>
              <div v-if="form.files && form.files.length" class="file-info">
                <div v-for="file in form.files" :key="file.name">
                  文件名: {{ file.name }}，大小: {{ formatFileSize(file.size) }}
                </div>
              </div>
              <el-button class="upload-button" type="primary">
                上传文件
              </el-button>
            </div>
          </div>
          <input
            ref="fileInput"
            type="file"
            style="display: none"
            multiple
            @change="handleFileChange"
          />
        </div>

        <!-- <div class="file-tips">
          <template v-if="!form.file">
            <el-alert
              title="请上传模型文件，支持以下格式：glb, gltf, obj, fbx, stl, 3ds, ply, dae"
              type="info"
              :closable="false"
              show-icon
            />
          </template>

          <div>
            如果模型文件包含附属文件，请将模型文件和所有附属文件全部选中，然后拖拽至框内。
          </div>
          <div>附属文件包括以下几类：</div>
          <ul>
            <li>所有模型格式附带的贴图文件，如 .png 、 .jpg 和 .jpeg 。</li>
            <li>obj格式附带的 .mtl 文件。</li>
            <li>gltf格式附带的 .bin 文件。</li>
            <li>shp格式附带的 .shx 、 .prj 、 .xml 和 .dbf 文件。</li>
          </ul>
        </div> -->
      </el-form-item>

      <el-form-item label="预览图" prop="preview">
        <el-upload
          class="preview-uploader"
          action="#"
          :show-file-list="false"
          :on-change="handlePreviewChange"
          :auto-upload="false"
          :multiple="false"
          accept="image/*"
        >
          <div class="w-full h-full">
            <div class="preview-box">
              <img v-if="previewUrl" :src="previewUrl" class="preview-image" />
              <template v-else>
                <el-icon class="preview-uploader-icon"><Plus /></el-icon>
                <div class="preview-text">点击上传预览图</div>
              </template>
            </div>
          </div>
        </el-upload>
        <div class="preview-tips">上传模型预览图，支持jpg、png格式</div>
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="visible = false">取消</el-button>
        <el-button type="primary" :loading="uploading" @click="handleSubmit">
          上传
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import FileModelService from "@/api/file";
import OSSService from "@/api/oss";
import { type FileModel, type ModelLibrary } from "@/modules";
import { detectModelFormat, formatModelTypeToLabel } from "@/utils/fileUtils";
import { message } from "@/utils/message";
import { Plus, Upload } from "@element-plus/icons-vue";
import type { FormInstance, UploadFile } from "element-plus";
import { ref } from "vue";

defineOptions({
  name: "UploadDialog"
});

interface Props {
  modelData: ModelLibrary;
  tempModelInfo: {
    name: string;
    category: string;
    lodLevel: string;
  };
}

// 扩展表单数据接口，添加文件和格式类型字段
interface UploadFormData extends FileModel {
  files?: File[];
  formatType?: string;
}

const { modelData, tempModelInfo } = defineProps<Props>();

const emits = defineEmits(["submit-success"]);
const visible = defineModel("visible", { default: false, required: true });
const fileInput = ref<HTMLInputElement | null>(null);
const formRef = ref<FormInstance>();
const uploading = ref(false);
const previewUrl = ref("");

const rules = {
  formatType: [{ required: true, message: "请选择模型格式", trigger: "change" }]
};

const form = ref<UploadFormData>({
  lodLevel: "",
  describe: "",
  files: [],
  modelLibraryId: modelData?.id
});

// 处理上传文件点击
const handleUploadClick = () => {
  fileInput.value?.click();
};

// 处理文件变更
const handleFileChange = async (event: Event) => {
  const target = event.target as HTMLInputElement;
  if (target.files && target.files.length > 0) {
    form.value.files = Array.from(target.files);

    // 尝试检测主文件格式（可选，不强制要求）
    const mainFile = form.value.files.find(f =>
      /\.(obj|gltf|glb|fbx|stl|3ds|ply|dae)$/i.test(f.name)
    );

    if (mainFile) {
      // 如果找到模型文件，检测格式
      const detectedFormat = detectModelFormat(mainFile);
      if (detectedFormat !== undefined) {
        form.value.format = detectedFormat;
        form.value.formatType =
          formatModelTypeToLabel(detectedFormat).toLowerCase();
      }
    } else {
      // 如果没有找到已知模型文件，使用第一个文件
      form.value.formatType = "unknown";
      message("未检测到已知模型格式，文件将作为附件上传", { type: "info" });
    }

    // 上传所有文件
    const result = await OSSService.modelUpload(form.value.files);
    if (result) {
      form.value.fileSize = formatFileSize(result.fileSize);
      form.value.address = result.filePath;
    }
  }
};

// 处理拖拽文件
const handleDrop = async (event: DragEvent) => {
  const files = event.dataTransfer?.files;
  if (files && files.length > 0) {
    form.value.files = Array.from(files);

    // 尝试检测主文件格式（可选，不强制要求）
    const mainFile = form.value.files.find(f =>
      /\.(obj|gltf|glb|fbx|stl|3ds|ply|dae)$/i.test(f.name)
    );

    if (mainFile) {
      // 如果找到模型文件，检测格式
      const detectedFormat = detectModelFormat(mainFile);
      if (detectedFormat !== undefined) {
        form.value.format = detectedFormat;
        form.value.formatType =
          formatModelTypeToLabel(detectedFormat).toLowerCase();
      }
    } else {
      // 如果没有找到已知模型文件，使用第一个文件
      form.value.formatType = "unknown";
      message("未检测到已知模型格式，文件将作为附件上传", { type: "info" });
    }

    // 上传所有文件
    const result = await OSSService.modelUpload(form.value.files);
    if (result) {
      form.value.fileSize = formatFileSize(result.fileSize);
      form.value.address = result.filePath;
    }
  }
};

// 处理预览图上传
const handlePreviewChange = async (file: UploadFile) => {
  previewUrl.value = URL.createObjectURL(file.raw);
  try {
    const result = await OSSService.upload(file.raw);
    if (result && result.length > 0) {
      form.value.previewImage = result[0].filePath;
    }
  } catch (error) {
    message("预览图上传失败", { type: "error" });
    console.error(error);
  }
};

// 格式化文件大小
const formatFileSize = (size: number) => {
  if (size < 1024) {
    return size + " B";
  } else if (size < 1024 * 1024) {
    return (size / 1024).toFixed(2) + " KB";
  } else if (size < 1024 * 1024 * 1024) {
    return (size / (1024 * 1024)).toFixed(2) + " MB";
  } else {
    return (size / (1024 * 1024 * 1024)).toFixed(2) + " GB";
  }
};

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return;

  await formRef.value.validate(async (valid, fields) => {
    if (!valid) {
      return;
    }

    uploading.value = true;
    try {
      await FileModelService.add(form.value);
      visible.value = false;
      emits("submit-success");
    } catch (error) {}

    uploading.value = false;
  });
};
</script>

<style lang="scss" scoped>
.model-info {
  border: 1px solid #ebeef5;
  border-radius: 4px;
  padding: 15px;
  margin-bottom: 20px;
  background-color: #f9f9f9;

  h3 {
    margin-top: 0;
    margin-bottom: 10px;
    font-size: 18px;
    color: #303133;
  }

  p {
    margin: 5px 0;
    color: #606266;
  }
}

.upload-container {
  margin-bottom: 15px;
}

.upload-box {
  width: 100%;
  height: 200px;
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: border-color 0.3s;

  &:hover {
    border-color: var(--el-color-primary);
  }

  .upload-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
  }

  .upload-icon {
    font-size: 48px;
    color: #c0c4cc;
    margin-bottom: 10px;
  }

  .upload-text {
    color: #606266;
    font-size: 14px;
    margin-bottom: 20px;
  }

  .file-info {
    text-align: center;
    margin-bottom: 20px;
    color: #606266;
    font-size: 14px;
  }
}

.file-tips {
  margin-top: 10px;
  margin-left: 10px;
  color: #606266;
  font-size: 12px;
  line-height: 1.6;

  ul {
    padding-left: 20px;
    margin: 5px 0;
  }
}

.preview-uploader {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 200px;
  height: 200px;
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: border-color 0.3s;

  &:hover {
    border-color: var(--el-color-primary);
  }

  .preview-box {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
  }

  .preview-uploader-icon {
    font-size: 28px;
    color: #8c939d;
  }

  .preview-text {
    position: absolute;
    bottom: 15px;
    color: #606266;
    font-size: 12px;
  }

  .preview-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
}

.preview-tips {
  margin-top: 10px;
  color: #606266;
  font-size: 12px;
  margin-left: 10px;
}
</style>
