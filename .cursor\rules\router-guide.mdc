---
description:
globs:
alwaysApply: false
---
# Router Structure Guide

The application uses Vue Router with a modular structure. Router configuration is split into modules for better organization.

## Main Router Files

- [src/router/index.ts](mdc:src/router/index.ts): Main router configuration and navigation guards
- [src/router/utils.ts](mdc:src/router/utils.ts): Router utility functions

## Router Modules

All route modules are automatically imported from the `src/router/modules` directory. Each module should export a default route configuration that follows the Vue Router structure.

## Route Structure

- Routes are organized in a hierarchical tree structure
- The router automatically flattens nested routes to two-level routes for better performance
- Each route can have meta information for permissions, titles, icons, etc.

## Navigation Guards

The router implements several navigation guards:
- Authentication check
- Permission validation
- External link handling
- Document title setting
- Keep-alive route handling
