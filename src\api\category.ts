import type { Category } from "@/modules";
import { http } from "@/utils/http";
import type { Result, ListQuery, ListResult } from "@/utils/models";

class CategoryService {
  categoryUrl = "/api/modelCategory";

  async getList(param?: string | ListQuery): Promise<ListResult<Category>> {
    const endpoint =
      typeof param === "string"
        ? `${this.categoryUrl}/getList`
        : this.categoryUrl;

    const params = typeof param === "string" ? { key: param } : param;

    return await http.get<ListQuery, ListResult<Category>>(endpoint, {
      params
    });
  }

  async getAllCategories(): Promise<ListResult<Category>> {
    return await http.get<null, ListResult<Category>>(
      `${this.categoryUrl}/getList`
    );
  }

  async add(data: Category) {
    const res = await http.post<Category, Result<number>>(
      `${this.categoryUrl}`,
      {
        data
      }
    );
    return res;
  }

  async update(data: Category) {
    const res = await http.put<Category, ListResult<Category>>(
      `${this.categoryUrl}`,
      data
    );
    return res;
  }

  async delete(id: number) {
    const res = await http.delete<Result<number>>(`${this.categoryUrl}`, id);
    return res;
  }
}

const categoryService = new CategoryService();

export default categoryService;
