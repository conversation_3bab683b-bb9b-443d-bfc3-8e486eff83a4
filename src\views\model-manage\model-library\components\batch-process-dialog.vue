<template>
  <el-dialog v-model="visible" title="批量处理模型" width="50%">
    <div class="dialog-content">
      <div class="models-summary">
        <el-alert type="info" :closable="false" show-icon>
          <template #title>
            已选择 {{ models.length }} 个模型进行批量处理
          </template>
          <p>您可以选择下方的处理选项，对所有选中的模型进行批量处理。</p>
        </el-alert>

        <el-collapse v-if="models.length > 0">
          <el-collapse-item title="查看选中的模型" name="1">
            <el-table :data="models" style="width: 100%" max-height="250">
              <el-table-column prop="id" label="ID" width="60" />
              <el-table-column prop="name" label="模型名称" />
              <el-table-column
                prop="originalType"
                label="原始类型"
                width="100"
              />
              <el-table-column prop="fileSize" label="文件大小" width="100" />
              <el-table-column prop="result" label="当前状态" width="120">
                <template #default="{ row }">
                  <el-tag
                    :type="
                      row.result === '处理成功'
                        ? 'success'
                        : row.result === '处理中'
                          ? 'warning'
                          : 'danger'
                    "
                    size="small"
                  >
                    {{ row.result }}
                  </el-tag>
                </template>
              </el-table-column>
            </el-table>
          </el-collapse-item>
        </el-collapse>
      </div>

      <div class="process-options">
        <h3>处理选项</h3>
        <el-form :model="form" label-position="top">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="处理类型">
                <el-radio-group v-model="form.processType">
                  <el-radio label="optimize">优化模型</el-radio>
                  <el-radio label="convert">格式转换</el-radio>
                  <el-radio label="both">优化并转换</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="处理优先级">
                <el-select v-model="form.priority" style="width: 100%">
                  <el-option label="低" value="low" />
                  <el-option label="中" value="medium" />
                  <el-option label="高" value="high" />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>

          <template v-if="form.processType !== 'convert'">
            <h4>优化选项</h4>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="优化方式">
                  <el-checkbox-group v-model="form.optimizeOptions">
                    <div class="checkbox-item">
                      <el-checkbox label="reduceMesh">减面处理</el-checkbox>
                    </div>
                    <div class="checkbox-item">
                      <el-checkbox label="fixNormals">修复法线</el-checkbox>
                    </div>
                    <div class="checkbox-item">
                      <el-checkbox label="optimizeTextures"
                        >优化纹理</el-checkbox
                      >
                    </div>
                    <div class="checkbox-item">
                      <el-checkbox label="removeUnused"
                        >删除未使用材质</el-checkbox
                      >
                    </div>
                  </el-checkbox-group>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item
                  v-if="form.optimizeOptions.includes('reduceMesh')"
                  label="优化程度"
                >
                  <el-slider
                    v-model="form.optimizeLevel"
                    :min="1"
                    :max="5"
                    :marks="{ 1: '轻微', 3: '中等', 5: '极致' }"
                  />
                </el-form-item>
                <el-form-item
                  v-if="form.optimizeOptions.includes('optimizeTextures')"
                  label="纹理质量"
                >
                  <el-select v-model="form.textureQuality" style="width: 100%">
                    <el-option label="原始质量" value="original" />
                    <el-option label="高质量" value="high" />
                    <el-option label="中等质量" value="medium" />
                    <el-option label="低质量" value="low" />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
          </template>

          <template v-if="form.processType !== 'optimize'">
            <h4>转换选项</h4>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="目标格式">
                  <el-select v-model="form.targetFormat" style="width: 100%">
                    <el-option label="glTF/GLB" value="glb" />
                    <el-option label="OBJ" value="obj" />
                    <el-option label="FBX" value="fbx" />
                    <el-option label="STL" value="stl" />
                    <el-option label="USDZ" value="usdz" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="压缩选项">
                  <el-select v-model="form.compression" style="width: 100%">
                    <el-option label="无压缩" value="none" />
                    <el-option label="Draco压缩" value="draco" />
                    <el-option label="Meshopt压缩" value="meshopt" />
                    <el-option label="GZIP压缩" value="gzip" />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
          </template>

          <el-form-item label="处理完成后">
            <el-checkbox-group v-model="form.postProcess">
              <div class="checkbox-item">
                <el-checkbox label="notify">发送通知</el-checkbox>
              </div>
              <div class="checkbox-item">
                <el-checkbox label="export">导出处理结果</el-checkbox>
              </div>
              <div class="checkbox-item">
                <el-checkbox label="archive">归档原始模型</el-checkbox>
              </div>
            </el-checkbox-group>
          </el-form-item>
        </el-form>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="visible = false">取消</el-button>
        <el-button type="primary" :loading="processing" @click="startProcess">
          开始处理
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref } from "vue";
import { ElMessage } from "element-plus";

defineOptions({
  name: "BatchProcessDialog"
});

const props = defineProps({
  models: {
    type: Array,
    default: () => []
  }
});

const emit = defineEmits(["process-success"]);

const visible = defineModel("visible", { default: false, required: true });
const processing = ref(false);

const form = ref({
  processType: "optimize",
  priority: "medium",
  optimizeOptions: ["reduceMesh", "optimizeTextures"],
  optimizeLevel: 3,
  textureQuality: "medium",
  targetFormat: "glb",
  compression: "none",
  postProcess: ["notify"]
});

const startProcess = () => {
  if (props.models.length === 0) {
    ElMessage.warning("请至少选择一个模型进行处理");
    return;
  }

  processing.value = true;

  // 模拟处理过程
  setTimeout(() => {
    processing.value = false;
    visible.value = false;

    ElMessage.success({
      message: `已开始处理 ${props.models.length} 个模型，处理完成后将通知您`,
      duration: 3000
    });
    emit("process-success");
  }, 2000);
};
</script>

<style lang="scss" scoped>
.dialog-content {
  max-height: 60vh;
  overflow-y: auto;
}

.models-summary {
  margin-bottom: 20px;
}

.process-options {
  h3 {
    margin-top: 0;
    margin-bottom: 15px;
    font-size: 16px;
    color: #303133;
  }

  h4 {
    margin-top: 15px;
    margin-bottom: 10px;
    font-size: 14px;
    color: #606266;
  }
}

.checkbox-item {
  margin-bottom: 8px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}
</style>
