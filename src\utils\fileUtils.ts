import { ModelFormat } from "@/modules/model/file.model";

/**
 * 根据文件名或文件对象判断模型格式类型
 * @param file 文件对象或文件名
 * @returns 对应的ModelFormat枚举值，如果不是支持的格式则返回undefined
 */
export function detectModelFormat(
  file: File | string
): ModelFormat | undefined {
  let fileName: string;

  if (typeof file === "string") {
    fileName = file.toLowerCase();
  } else {
    fileName = file.name.toLowerCase();
  }

  // 获取文件扩展名
  const extension = fileName.split(".").pop()?.toLowerCase();

  if (!extension) {
    return undefined;
  }

  // 根据扩展名确定模型格式
  switch (extension) {
    case "glb":
      return ModelFormat.glb;
    case "gltf":
      return ModelFormat.gltf;
    case "obj":
      return ModelFormat.obj;
    case "fbx":
      return ModelFormat.fbx;
    case "stl":
      return ModelFormat.stl;
    case "dae":
      return ModelFormat.dae;
    case "ply":
      return ModelFormat.ply;
    case "x3d":
      return ModelFormat.x3d;
    case "usdz":
      return ModelFormat.usdz;
    default:
      return undefined;
  }
}

/**
 * 获取ModelFormat对应的文件扩展名
 * @param format ModelFormat枚举值
 * @returns 对应的文件扩展名(不包含点)，未知格式返回空字符串
 */
export function getExtensionFromFormat(format: ModelFormat): string {
  switch (format) {
    case ModelFormat.glb:
      return "glb";
    case ModelFormat.gltf:
      return "gltf";
    case ModelFormat.obj:
      return "obj";
    case ModelFormat.fbx:
      return "fbx";
    case ModelFormat.stl:
      return "stl";
    case ModelFormat.dae:
      return "dae";
    case ModelFormat.ply:
      return "ply";
    case ModelFormat.x3d:
      return "x3d";
    case ModelFormat.usdz:
      return "usdz";
    default:
      return "";
  }
}

/**
 * 将模型格式转换为显示文本
 * @param format ModelFormat枚举值
 * @returns 对应的显示文本，未知格式返回"未知格式"
 */
export function formatModelTypeToLabel(format?: ModelFormat): string {
  if (format === undefined) return "未知格式";

  switch (format) {
    case ModelFormat.glb:
      return "GLB";
    case ModelFormat.gltf:
      return "GLTF";
    case ModelFormat.obj:
      return "OBJ";
    case ModelFormat.fbx:
      return "FBX";
    case ModelFormat.stl:
      return "STL";
    case ModelFormat.dae:
      return "DAE";
    case ModelFormat.ply:
      return "PLY";
    case ModelFormat.x3d:
      return "X3D";
    case ModelFormat.usdz:
      return "USDZ";
    default:
      return "未知格式";
  }
}
