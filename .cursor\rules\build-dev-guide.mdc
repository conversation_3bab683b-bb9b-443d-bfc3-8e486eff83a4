---
description:
globs:
alwaysApply: false
---
# Build and Development Guide

## Development

Development commands are defined in [package.json](mdc:package.json):

- `pnpm dev`: Start development server
- `pnpm serve`: Alias for dev
- `pnpm build`: Production build
- `pnpm build:staging`: Staging build
- `pnpm preview`: Preview production build
- `pnpm typecheck`: Run TypeScript type checking
- `pnpm lint`: Run all linters

## Build Configuration

Vite is used as the build tool:

- [vite.config.ts](mdc:vite.config.ts): Main Vite configuration
- [postcss.config.js](mdc:postcss.config.js): PostCSS configuration
- [tsconfig.json](mdc:tsconfig.json): TypeScript configuration

## Environment Variables

Environment-specific configuration:

- Environment variables are prefixed with `VITE_` 
- Build modes: development, staging, production

## Linting and Formatting

Code quality tools:

- ESLint: [eslint.config.js](mdc:eslint.config.js)
- Prettier: [.prettierrc.js](mdc:.prettierrc.js)
- Stylelint: [stylelint.config.js](mdc:stylelint.config.js)

## Deployment

Docker is available for containerized deployment:

- [Dockerfile](mdc:Dockerfile): Docker configuration
- [.dockerignore](mdc:.dockerignore): Files to exclude from Docker context
