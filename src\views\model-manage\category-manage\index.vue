<template>
  <div class="main">
    <PureTableBar
      title="分类管理"
      :columns="columns"
      @refresh="fetchCategories"
    >
      <template #buttons>
        <el-button type="primary" @click="handleAddCategory">
          新增分类
        </el-button>
      </template>
      <template v-slot="{ size, dynamicColumns }">
        <pure-table
          align-whole="center"
          showOverflowTooltip
          table-layout="auto"
          :loading="loading"
          :size="size"
          adaptive
          :adaptiveConfig="{ offsetBottom: 108 }"
          :data="categories"
          :columns="dynamicColumns"
          row-key="id"
          default-expand-all
          :header-cell-style="{
            background: 'var(--el-fill-color-light)',
            color: 'var(--el-text-color-primary)'
          }"
        >
          <template #operation="{ row }">
            <el-button
              class="reset-margin"
              link
              type="primary"
              :size="size"
              @click="handleAddSubCategory(row)"
            >
              添加子分类
            </el-button>
            <el-button
              class="reset-margin"
              link
              type="primary"
              :size="size"
              @click="handleEditCategory(row)"
            >
              编辑
            </el-button>
            <el-button
              class="reset-margin"
              link
              type="danger"
              :size="size"
              @click="handleDeleteCategory(row)"
            >
              删除
            </el-button>
          </template>
        </pure-table>
      </template>
    </PureTableBar>

    <CategoryFormDialog
      v-if="dialogVisible"
      v-model="dialogVisible"
      :is-edit="isEdit"
      :category-data="currentCategory"
      :parent-id="parentCategoryId"
      @submit-success="handleSubmitSuccess"
    />
  </div>
</template>

<script setup lang="ts">
import { PureTableBar } from "@/components/RePureTableBar";
import { message } from "@/utils/message";
import { ElMessageBox } from "element-plus";
import { onMounted, reactive, ref } from "vue";
import CategoryFormDialog from "./components/CategoryFormDialog.vue";
import categoryService from "@/api/category";
import { Category } from "@/modules";
import { handleTree } from "@/utils/tree";

defineOptions({
  name: "ModelCategory"
});

const loading = ref(true);
const categories = ref([]);
const dialogVisible = ref(false);
const isEdit = ref(false);
const currentCategory = ref<Category | null>(null);
const parentCategoryId = ref<number | null>(null);

const columns = [
  {
    label: "分类名称",
    prop: "name",
    minWidth: 120
  },
  {
    label: "分类码",
    prop: "code",
    minWidth: 100
  },
  {
    label: "排序",
    prop: "sort",
    minWidth: 80
  },
  {
    label: "描述",
    prop: "describe",
    minWidth: 150
  },
  {
    label: "操作",
    fixed: "right",
    width: 220,
    slot: "operation"
  }
] as any;

const fetchCategories = async () => {
  loading.value = true;
  try {
    const res = await categoryService.getAllCategories();
    const allCategories = res.data || [];
    categories.value = handleTree(allCategories);
  } catch (error) {
    console.error("Failed to fetch categories:", error);
    message("获取分类列表失败", { type: "error" });
    categories.value = [];
  } finally {
    loading.value = false;
  }
};

function handleAddCategory() {
  isEdit.value = false;
  currentCategory.value = null;
  parentCategoryId.value = null;
  dialogVisible.value = true;
}

function handleAddSubCategory(row: Category) {
  isEdit.value = false;
  currentCategory.value = null;
  parentCategoryId.value = row.id;
  dialogVisible.value = true;
}

function handleEditCategory(row: Category) {
  isEdit.value = true;
  currentCategory.value = { ...row };
  parentCategoryId.value = null;
  dialogVisible.value = true;
}

async function handleDeleteCategory(row) {
  try {
    await ElMessageBox.confirm(
      `确定要删除分类 "${row.name}" 吗？`,
      "删除确认",
      {
        confirmButtonText: "删除",
        cancelButtonText: "取消",
        type: "warning"
      }
    );
    await categoryService.delete(row.id);
    message(`删除成功`, { type: "success" });
    fetchCategories();
  } catch (error) {
    if (error !== "cancel") {
      console.error("Failed to delete category:", error);
      message("删除失败", { type: "error" });
    }
  }
}

function handleSubmitSuccess() {
  fetchCategories();
}

onMounted(() => {
  fetchCategories();
});
</script>
