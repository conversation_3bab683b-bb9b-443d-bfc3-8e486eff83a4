import type { ModelFileLog } from "@/modules";
import { http } from "@/utils/http";
import type { ListQuery, ListResult } from "@/utils/models";

class LogService {
  categoryUrl = "/api/sysLog";

  async getList(param?: string | ListQuery): Promise<ListResult<ModelFileLog>> {
    const endpoint =
      typeof param === "string"
        ? `${this.categoryUrl}/getList`
        : this.categoryUrl;

    const params = typeof param === "string" ? { key: param } : param;

    return await http.get<ListQuery, ListResult<ModelFileLog>>(endpoint, {
      params
    });
  }
}

export default new LogService();
