<template>
  <el-dialog v-model="visible" title="下载模型" width="80%">
    <div v-if="modelData">
      <div class="model-info">
        <h3>{{ modelData.name }}</h3>
        <p>分类: {{ modelData.modelCategory?.name }}</p>
      </div>
      <el-divider content-position="center">可用格式</el-divider>
      <el-table :data="modelFiles" style="width: 100%">
        <el-table-column prop="type" label="格式" width="100">
          <template #default="{ row }">
            {{ formatModelTypeToLabel(row.format) }}
          </template>
        </el-table-column>
        <el-table-column prop="lodLevel" label="LOD等级" width="100" />
        <el-table-column prop="describe" label="备注" />
        <el-table-column prop="fileSize" label="大小" width="90" />
        <el-table-column prop="uploader" label="上传人" width="80" />
        <el-table-column prop="uploadTime" label="上传时间" width="250" />
        <el-table-column label="操作" width="200">
          <template #default="{ row }">
            <el-button
              type="primary"
              link
              :loading="downloading === row.type"
              @click="handleFormatDownload(row)"
            >
              下载
            </el-button>
            <el-button type="success" link @click="handleEditFile(row)">
              编辑
            </el-button>
            <el-button type="danger" link @click="handleDeleteFormat(row)">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 文件编辑对话框 -->
    <FileEditDialog
      v-if="editDialogVisible"
      :visible="editDialogVisible"
      :file-data="currentEditFile"
      @update:visible="editDialogVisible = $event"
      @submit-success="handleEditSuccess"
    />
  </el-dialog>
</template>

<script setup lang="ts">
import FileModelService from "@/api/file";
import { FileModel, ModelLibrary } from "@/modules";
import { message } from "@/utils/message";
import { ElMessageBox } from "element-plus";
import { onMounted, ref } from "vue";
import { detectModelFormat, formatModelTypeToLabel } from "@/utils/fileUtils";
import FileEditDialog from "./file-edit-dialog.vue";

defineOptions({
  name: "DownloadDialog"
});

const props = defineProps<{
  modelData: ModelLibrary;
}>();

const modelFiles = ref<FileModel[]>([]);

const emit = defineEmits(["update-downloads"]);

const visible = defineModel({ default: false, required: true });
const downloading = ref(null);
const editDialogVisible = ref(false);
const currentEditFile = ref<FileModel | null>(null);

const handleFormatDownload = async (format: FileModel) => {
  downloading.value = format.format;
  try {
    await FileModelService.download(format.id);
    message(`${format.describe || "文件"} 下载成功`, { type: "success" });
  } catch (error) {
    message(`下载失败: ${error.message}`, { type: "error" });
  } finally {
    downloading.value = null;
  }
};

const handleDeleteFormat = (format: FileModel) => {
  ElMessageBox.confirm(`确定要删除吗？`, "删除确认", {
    confirmButtonText: "删除",
    cancelButtonText: "取消",
    type: "warning"
  })
    .then(async () => {
      await FileModelService.delete(format.id);
      await getData();
    })
    .catch(() => {});
};

// 处理编辑文件
const handleEditFile = (file: FileModel) => {
  console.log("handleEditFile called with:", file);
  currentEditFile.value = file;
  editDialogVisible.value = true;
  console.log("currentEditFile.value:", currentEditFile.value);
  console.log("editDialogVisible.value:", editDialogVisible.value);
};

// 处理编辑成功
const handleEditSuccess = async () => {
  await getData();
  message("文件信息更新成功", { type: "success" });
};

async function getData() {
  const { data } = await FileModelService.getAllByLibraryId(props.modelData.id);
  modelFiles.value = data;
}

onMounted(async () => {
  await getData();
});
</script>

<style lang="scss" scoped>
.model-info {
  border: 1px solid #ebeef5;
  border-radius: 4px;
  padding: 15px;
  margin-bottom: 20px;
  background-color: #f9f9f9;

  h3 {
    margin-top: 0;
    margin-bottom: 10px;
    font-size: 18px;
    color: #303133;
  }

  p {
    margin: 5px 0;
    color: #606266;
  }
}

.drawer-content {
  padding: 20px;

  h3 {
    margin-bottom: 20px;
    color: #303133;
  }
}
</style>
