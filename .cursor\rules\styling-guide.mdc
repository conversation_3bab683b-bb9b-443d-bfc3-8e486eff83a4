---
description:
globs:
alwaysApply: false
---
# Styling Guide

The application uses a combination of SCSS, Tailwind CSS, and Element Plus for styling.

## Style Files

- [src/style/index.scss](mdc:src/style/index.scss): Main stylesheet with global styles
- [src/style/reset.scss](mdc:src/style/reset.scss): CSS reset
- [src/style/tailwind.css](mdc:src/style/tailwind.css): Tailwind CSS imports

## Tailwind CSS

The project uses Tailwind CSS for utility-first styling. The configuration is in:
- [tailwind.config.ts](mdc:tailwind.config.ts): Tailwind configuration

## CSS Preprocessors

SCSS is used as the CSS preprocessor. Components can include scoped styles:

```vue
<style scoped lang="scss">
/* Component-specific styles */
</style>
```

## Element Plus Theming

Element Plus components are used throughout the application with some custom theming:
- Element Plus styles are imported in [src/main.ts](mdc:src/main.ts)
- Custom theme variables may be defined in the style directory

## Style Linting

The project uses stylelint for CSS/SCSS linting:
- [stylelint.config.js](mdc:stylelint.config.js): Stylelint configuration
- [.stylelintignore](mdc:.stylelintignore): Files to ignore for stylelint
