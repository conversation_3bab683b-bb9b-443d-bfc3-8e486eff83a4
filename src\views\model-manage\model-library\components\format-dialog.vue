<template>
  <el-dialog v-model="visible" title="添加模型格式" width="50%">
    <div v-if="modelData">
      <div class="model-info">
        <h3>{{ modelData.name }}</h3>
        <p>模型分类: {{ modelData.category }}</p>
        <p>LOD等级: {{ modelData.lodLevel }}</p>
        <p>
          现有格式:
          <el-tag
            v-for="format in modelData.formats"
            :key="format.type"
            class="ml-1"
            :type="format.type === modelData.primaryFormat ? 'success' : 'info'"
          >
            {{ format.type }}
          </el-tag>
        </p>
      </div>

      <el-divider content-position="center">新增格式</el-divider>

      <el-form ref="formRef" :model="form" label-width="120px" :rules="rules">
        <el-form-item label="文件格式" prop="formatType">
          <el-select v-model="form.formatType" placeholder="选择文件格式">
            <el-option label="GLB" value="glb" />
            <el-option label="GLTF" value="gltf" />
            <el-option label="OBJ" value="obj" />
            <el-option label="STL" value="stl" />
            <el-option label="FBX" value="fbx" />
            <el-option label="PLY" value="ply" />
            <el-option label="3DS" value="3ds" />
            <el-option label="USDZ" value="usdz" />
          </el-select>
        </el-form-item>

        <el-form-item label="上传文件" prop="file">
          <input
            ref="fileInput"
            type="file"
            style="display: none"
            multiple
            @change="handleFileChange"
          />
          <!-- <el-upload
            class="upload-container"
            action="#"
            :auto-upload="false"
            :limit="1"
            :on-change="handleFileChange"
          >
            <template #trigger>
              <el-button type="primary">选择文件</el-button>
            </template>
            <template #tip>
              <div class="el-upload__tip">请上传对应格式的3D模型文件</div>
            </template>
          </el-upload> -->
        </el-form-item>

        <el-form-item label="备注" prop="note">
          <el-input
            v-model="form.note"
            type="textarea"
            placeholder="请输入该格式模型的用途、特点等备注信息"
            :rows="3"
          />
        </el-form-item>

        <el-divider>高级选项</el-divider>

        <el-form-item label="精度级别">
          <el-select v-model="form.precision" placeholder="选择精度级别">
            <el-option label="原始精度" value="original" />
            <el-option label="高精度" value="high" />
            <el-option label="中等精度" value="medium" />
            <el-option label="低精度" value="low" />
          </el-select>
        </el-form-item>

        <el-form-item label="用途标签">
          <el-checkbox-group v-model="form.usageTags">
            <el-checkbox label="web">Web使用</el-checkbox>
            <el-checkbox label="ar">AR应用</el-checkbox>
            <el-checkbox label="vr">VR应用</el-checkbox>
            <el-checkbox label="print">3D打印</el-checkbox>
            <el-checkbox label="game">游戏引擎</el-checkbox>
          </el-checkbox-group>
        </el-form-item>
      </el-form>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="visible = false">取消</el-button>
        <el-button type="primary" :loading="uploading" @click="handleSubmit">
          上传
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, watch } from "vue";
import { message } from "@/utils/message";
import type { FormInstance, FormRules } from "element-plus";
import { detectModelFormat, formatModelTypeToLabel } from "@/utils/fileUtils";
import { FileModel, ModelFormat } from "@/modules";

defineOptions({
  name: "FormatDialog"
});

const props = defineProps({
  modelData: {
    type: Object,
    default: null
  }
});

const emits = defineEmits(["submit-success"]);
const visible = defineModel("visible", { default: false, required: true });
const uploading = ref(false);
const formRef = ref<FormInstance>();

const form = ref({
  formatType: "",
  file: null,
  note: "",
  setPrimary: false,
  precision: "original",
  usageTags: [],
  mainFile: undefined,
  relatedFiles: [] as File[],
  format: undefined
});

// 表单验证规则
const rules = ref<FormRules>({
  formatType: [
    { required: true, message: "请选择文件格式", trigger: "change" }
  ],
  file: [{ required: true, message: "请上传文件", trigger: "change" }]
});

// 每次打开对话框时重置表单
watch(
  () => visible.value,
  newValue => {
    if (newValue) {
      form.value = {
        formatType: "",
        file: null,
        note: "",
        setPrimary: false,
        precision: "original",
        usageTags: [],
        mainFile: undefined,
        relatedFiles: [],
        format: undefined
      };
      if (formRef.value) {
        formRef.value.resetFields();
      }
    }
  }
);

// 扩展表单数据接口，添加主文件和附属文件
interface UploadFormData extends FileModel {
  mainFile?: File;
  relatedFiles?: File[];
  formatType?: string;
}

// 处理文件变更 - 支持多文件选择
const handleFileChange = (event: Event) => {
  const target = event.target as HTMLInputElement;
  if (target.files && target.files.length > 0) {
    const files = Array.from(target.files);
    processUploadedFiles(files);
  }
};

// 处理拖拽文件 - 支持多文件拖放
const handleDrop = (event: DragEvent) => {
  const files = event.dataTransfer?.files;
  if (files && files.length > 0) {
    processUploadedFiles(Array.from(files));
  }
};

// 处理上传的文件组
const processUploadedFiles = (files: File[]) => {
  // 按文件扩展名分类
  const filesByExt: Record<string, File[]> = {};

  files.forEach(file => {
    const ext = file.name.split(".").pop()?.toLowerCase() || "";
    if (!filesByExt[ext]) filesByExt[ext] = [];
    filesByExt[ext].push(file);
  });

  // 确定主文件
  let mainFile: File | undefined;
  let mainFileFormat: ModelFormat | undefined;

  // 按优先级尝试找到主文件
  const priorityFormats = [
    "gltf",
    "glb",
    "obj",
    "fbx",
    "stl",
    "dae",
    "ply",
    "x3d",
    "usdz"
  ];

  for (const ext of priorityFormats) {
    if (filesByExt[ext]?.[0]) {
      mainFile = filesByExt[ext][0];
      mainFileFormat = detectModelFormat(mainFile);
      break;
    }
  }

  // 如果没找到已知格式的主文件，使用第一个文件
  if (!mainFile && files.length > 0) {
    mainFile = files[0];
    mainFileFormat = detectModelFormat(mainFile);
  }

  if (!mainFile) return;

  // 设置主文件和关联文件
  form.value.mainFile = mainFile;
  form.value.file = mainFile; // 兼容现有代码
  form.value.relatedFiles = files.filter(f => f !== mainFile);
  form.value.format = mainFileFormat;

  if (mainFileFormat !== undefined) {
    form.value.formatType =
      formatModelTypeToLabel(mainFileFormat).toLowerCase();

    // 根据文件数量给出提示
    if (files.length > 1) {
      message(
        `已自动检测模型格式: ${formatModelTypeToLabel(mainFileFormat)}, 上传了${files.length}个相关文件`,
        { type: "info" }
      );
    } else {
      message(`已自动检测模型格式: ${formatModelTypeToLabel(mainFileFormat)}`, {
        type: "info"
      });
    }
  } else {
    // 允许上传任意文件，即使无法识别格式
    form.value.formatType = "unknown";
    if (files.length > 1) {
      message(`已上传${files.length}个文件，未能识别模型格式，将作为附件处理`, {
        type: "info"
      });
    } else {
      message("未能识别模型格式，文件将作为附件处理", {
        type: "info"
      });
    }
  }
};

const handleSubmit = () => {
  if (!formRef.value) return;

  formRef.value.validate(valid => {
    new Promise(resolve => {
      if (!valid) {
        resolve(false);
      }

      uploading.value = true;

      // 模拟上传过程
      setTimeout(() => {
        uploading.value = false;
        message(
          `已成功为模型 ${props.modelData.name} 添加 ${form.value.formatType} 格式`,
          { type: "success" }
        );
        visible.value = false;
        emits("submit-success");
      }, 1500);
      resolve(true);
    });
  });
};
</script>

<style lang="scss" scoped>
.model-info {
  border: 1px solid #ebeef5;
  border-radius: 4px;
  padding: 15px;
  margin-bottom: 20px;
  background-color: #f9f9f9;

  h3 {
    margin-top: 0;
    margin-bottom: 10px;
    font-size: 18px;
    color: #303133;
  }

  p {
    margin: 5px 0;
    color: #606266;
  }
}

.upload-container {
  width: 100%;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}
</style>
