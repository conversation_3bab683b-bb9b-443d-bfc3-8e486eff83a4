---
description:
globs:
alwaysApply: false
---
# State Management Guide

The application uses Pinia for state management, organized in a modular way.

## Store Setup

- [src/store/index.ts](mdc:src/store/index.ts): Main store setup and initialization

## Store Modules

The store is divided into modules for different features:

- `/src/store/modules`: Contains all store modules
- Each module follows the Pinia store pattern with state, getters, and actions

## Key Store Modules

- Permission store: Manages user permissions and accessible routes
- User store: Manages user information and authentication state
- MultTags store: Manages the multi-tab navigation system
- App store: Manages global app settings

## Usage Pattern

Stores are typically accessed through composition API hooks:

```typescript
// Example of using a store
import { useUserStoreHook } from "@/store/modules/user";

// In a component
const userStore = useUserStoreHook();
const username = userStore.username;
```
