<template>
  <div class="main">
    <el-row :gutter="20">
      <el-col :span="24">
        <el-card class="overview-card">
          <div class="filter-container">
            <div class="date-filter">
              <el-radio-group
                v-model="dateRange"
                @change="handleDateRangeChange"
              >
                <el-radio-button value="week">最近7天</el-radio-button>
                <el-radio-button value="month">最近30天</el-radio-button>
                <el-radio-button value="quarter">最近90天</el-radio-button>
                <el-radio-button value="custom">自定义</el-radio-button>
              </el-radio-group>

              <el-date-picker
                v-if="dateRange === 'custom'"
                v-model="customDateRange"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                value-format="YYYY-MM-DD"
                @change="handleCustomDateChange"
              />
            </div>

            <div class="model-filter">
              <el-select
                v-model="selectedModel"
                placeholder="选择模型（默认全部）"
                style="width: 300px"
                clearable
                @change="fetchTimeBasedData"
              >
                <el-option
                  v-for="model in modelOptions"
                  :key="model.id"
                  :label="model.name"
                  :value="model.id"
                >
                  <span>{{ model.name }}</span>
                </el-option>
              </el-select>
            </div>
          </div>
          <div class="chart-container">
            <div ref="trendChartRef" class="trend-chart" />
          </div>
        </el-card>
      </el-col>
    </el-row>

    <el-row :gutter="20" class="mt-4">
      <el-col :span="12">
        <el-card class="model-ranking-card">
          <div class="card-header">
            <h3>热门模型排行</h3>
            <el-radio-group
              v-model="rankingType"
              size="small"
              @change="updateRankingChart"
            >
              <el-radio-button value="downloads">按下载量</el-radio-button>
            </el-radio-group>
          </div>
          <div ref="modelRankingChartRef" class="model-ranking-chart" />
        </el-card>
      </el-col>

      <el-col :span="12">
        <el-card class="category-distribution-card">
          <div class="card-header">
            <h3>分类分布</h3>
            <el-radio-group
              v-model="distributionType"
              size="small"
              @change="updateDistributionChart"
            >
              <el-radio-button value="models">按模型数量</el-radio-button>
              <el-radio-button value="downloads">按下载量</el-radio-button>
            </el-radio-group>
          </div>
          <div
            ref="categoryDistributionChartRef"
            class="category-distribution-chart"
          />
        </el-card>
      </el-col>
    </el-row>

    <el-row :gutter="20" class="mt-4">
      <el-col :span="12">
        <el-card class="format-distribution-card">
          <div class="card-header">
            <h3 style="width: 100%">格式分布</h3>
            <el-select
              v-model="formatTimeRange"
              size="small"
              @change="updateFormatChart"
            >
              <el-option label="全部时间" value="all" />
              <el-option label="最近30天" value="month" />
              <el-option label="最近7天" value="week" />
            </el-select>
          </div>
          <div
            ref="formatDistributionChartRef"
            class="format-distribution-chart"
          />
        </el-card>
      </el-col>

      <el-col :span="12">
        <el-card class="popular-format-card">
          <div class="card-header">
            <h3>热门格式上传</h3>
          </div>
          <el-table :data="formatUploadStats" style="width: 100%" size="small">
            <el-table-column prop="format" label="格式">
              <template #default="{ row }">
                <el-tag>{{ row.format }}</el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="uploadCount" label="上传次数" />
          </el-table>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup>
import libraryService from "@/api/model";
import statisticsService from "@/api/statistics";
import { message } from "@/utils/message";
import * as echarts from "echarts";
import { nextTick, onMounted, reactive, ref } from "vue";

defineOptions({
  name: "ModelStatistics"
});

// 图表实例
let trendChart = null;
let modelRankingChart = null;
let categoryDistributionChart = null;
let formatDistributionChart = null;

// DOM引用
const trendChartRef = ref(null);
const modelRankingChartRef = ref(null);
const categoryDistributionChartRef = ref(null);
const formatDistributionChartRef = ref(null);

// 筛选条件
const dateRange = ref("week");

// 初始化自定义日期范围为最近7天
const getDefaultDateRange = () => {
  const endDate = new Date();
  const startDate = new Date();
  startDate.setDate(startDate.getDate() - 7);

  // 格式化为 YYYY-MM-DD 格式
  const formatDate = date => {
    return date.toISOString().split("T")[0];
  };

  return [formatDate(startDate), formatDate(endDate)];
};

const customDateRange = ref(getDefaultDateRange());
const selectedModel = ref();
const rankingType = ref("downloads");
const distributionType = ref("models");
const formatTimeRange = ref("all");

// 数据
const overview = reactive({
  downloadCount: 0,
  uniqueUsers: 0
});

const modelOptions = ref([]);
const modelDetails = ref([]);
const trendData = ref({
  dates: [],
  downloads: []
});
const popularFormats = ref([]);
const categoryStats = ref([]);
const formatUploadStats = ref([]);

// 方法
function handleDateRangeChange() {
  fetchTimeBasedData();
}

function handleCustomDateChange() {
  fetchTimeBasedData();
}

function updateRankingChart() {
  if (!modelRankingChart) return;

  let data = [];
  let title = "";

  data = modelDetails.value
    .sort((a, b) => b.downloadCount - a.downloadCount)
    .slice(0, 10);
  title = "下载量Top10";

  const chartData = data.map(item => ({
    value: item.downloadCount,
    name: item.name
  }));

  modelRankingChart.setOption({
    title: {
      text: title,
      left: "center"
    },
    tooltip: {
      trigger: "axis",
      axisPointer: {
        type: "shadow"
      }
    },
    grid: {
      left: "3%",
      right: "4%",
      bottom: "3%",
      containLabel: true
    },
    xAxis: {
      type: "value"
    },
    yAxis: {
      type: "category",
      data: chartData.map(item => item.name).reverse(),
      axisLabel: {
        width: 100,
        overflow: "truncate"
      }
    },
    series: [
      {
        name: "下载量",
        type: "bar",
        data: chartData.map(item => item.value).reverse()
      }
    ]
  });
}

function updateDistributionChart() {
  if (!categoryDistributionChart) return;

  // 使用真实的分类统计数据
  const pieData = categoryStats.value.map(stat => ({
    name: stat.category,
    value:
      distributionType.value === "models"
        ? stat.modelCount || 0
        : stat.downloadCount || 0
  }));

  let title = "";
  switch (distributionType.value) {
    case "models":
      title = "模型数量分布";
      break;
    case "downloads":
      title = "下载量分布";
      break;
  }

  categoryDistributionChart.setOption({
    title: {
      text: title,
      left: "center"
    },
    tooltip: {
      trigger: "item",
      formatter: "{a} <br/>{b}: {c} ({d}%)"
    },
    legend: {
      orient: "horizontal",
      bottom: 0
    },
    series: [
      {
        name: title,
        type: "pie",
        radius: ["40%", "70%"],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 10,
          borderColor: "#fff",
          borderWidth: 2
        },
        label: {
          show: false
        },
        emphasis: {
          label: {
            show: true,
            fontSize: 14,
            fontWeight: "bold"
          }
        },
        labelLine: {
          show: false
        },
        data: pieData
      }
    ]
  });
}

async function updateFormatChart() {
  if (!formatDistributionChart) return;

  try {
    // 根据时间范围构建API参数
    let apiParams = {};

    if (formatTimeRange.value !== "all") {
      const endTime = new Date();
      const startTime = new Date();

      if (formatTimeRange.value === "week") {
        startTime.setDate(startTime.getDate() - 7);
      } else if (formatTimeRange.value === "month") {
        startTime.setDate(startTime.getDate() - 30);
      }

      apiParams = {
        startTime: startTime.toISOString(),
        endTime: endTime.toISOString()
      };
    }

    // 调用API获取格式上传统计数据
    const modelFormatDown = await statisticsService.getModelFormatDown(
      Object.keys(apiParams).length > 0 ? apiParams : undefined
    );

    // 处理API返回的数据
    let currentFormatStats = [];
    if (modelFormatDown.data && Array.isArray(modelFormatDown.data)) {
      currentFormatStats = modelFormatDown.data.map(item => ({
        format: item.x,
        uploadCount: item.y || 0
      }));
    }

    // 使用获取到的格式上传统计数据
    let formatData = currentFormatStats.map(stat => ({
      name: stat.format,
      value: stat.uploadCount
    }));

    // 排序并处理小数据分类
    formatData.sort((a, b) => b.value - a.value);

    if (formatData.length > 5) {
      const topFormats = formatData.slice(0, 5);
      const otherFormats = formatData.slice(5);
      const otherValue = otherFormats.reduce(
        (sum, item) => sum + item.value,
        0
      );

      formatData = [...topFormats, { name: "其他", value: otherValue }];
    }

    // 更新热门格式表格数据
    const totalUploads = formatData.reduce((sum, item) => sum + item.value, 0);
    popularFormats.value = formatData
      .map(item => ({
        type: item.name,
        uploads: item.value,
        percentage:
          totalUploads > 0 ? Math.round((item.value / totalUploads) * 100) : 0
      }))
      .sort((a, b) => b.uploads - a.uploads);

    formatDistributionChart.setOption({
      title: {
        text: "模型格式上传分布",
        left: "center"
      },
      tooltip: {
        trigger: "item",
        formatter: "{a} <br/>{b}: {c} ({d}%)"
      },
      legend: {
        orient: "horizontal",
        bottom: 0
      },
      series: [
        {
          name: "格式上传",
          type: "pie",
          radius: ["40%", "70%"],
          avoidLabelOverlap: false,
          itemStyle: {
            borderRadius: 10,
            borderColor: "#fff",
            borderWidth: 2
          },
          label: {
            show: false
          },
          emphasis: {
            label: {
              show: true,
              fontSize: 14,
              fontWeight: "bold"
            }
          },
          labelLine: {
            show: false
          },
          data: formatData
        }
      ]
    });
  } catch (error) {
    console.error("获取格式上传统计数据失败:", error);
    message.error("获取格式上传统计数据失败，请稍后重试");
  }
}

function initTrendChart() {
  if (!trendChartRef.value) return;

  trendChart = echarts.init(trendChartRef.value);

  trendChart.setOption({
    title: {
      text: "模型使用趋势"
    },
    tooltip: {
      trigger: "axis"
    },
    legend: {
      data: ["下载量"]
    },
    grid: {
      left: "3%",
      right: "4%",
      bottom: "3%",
      containLabel: true
    },
    xAxis: {
      type: "category",
      boundaryGap: false,
      data: trendData.value.dates
    },
    yAxis: {
      type: "value"
    },
    series: [
      {
        name: "下载量",
        type: "line",
        data: trendData.value.downloads,
        smooth: true,
        lineStyle: {
          width: 3
        }
      }
    ]
  });

  window.addEventListener("resize", () => {
    trendChart.resize();
  });
}

function initModelRankingChart() {
  if (!modelRankingChartRef.value) return;

  modelRankingChart = echarts.init(modelRankingChartRef.value);
  updateRankingChart();

  window.addEventListener("resize", () => {
    modelRankingChart.resize();
  });
}

function initCategoryDistributionChart() {
  if (!categoryDistributionChartRef.value) return;

  categoryDistributionChart = echarts.init(categoryDistributionChartRef.value);
  updateDistributionChart();

  window.addEventListener("resize", () => {
    categoryDistributionChart.resize();
  });
}

function initFormatDistributionChart() {
  if (!formatDistributionChartRef.value) return;

  formatDistributionChart = echarts.init(formatDistributionChartRef.value);
  updateFormatChart();

  window.addEventListener("resize", () => {
    formatDistributionChart.resize();
  });
}

// 获取与时间相关的统计数据（模型使用趋势）
async function fetchTimeBasedData() {
  try {
    // 计算时间范围
    let startTime, endTime;

    if (
      dateRange.value === "custom" &&
      customDateRange.value &&
      customDateRange.value.length === 2
    ) {
      // 自定义时间范围
      startTime = new Date(customDateRange.value[0] + "T00:00:00");
      endTime = new Date(customDateRange.value[1] + "T23:59:59");
    } else {
      // 预设时间范围
      let days = 7;
      if (dateRange.value === "month") days = 30;
      if (dateRange.value === "quarter") days = 90;

      endTime = new Date();
      startTime = new Date();
      startTime.setDate(startTime.getDate() - days);
    }

    // 获取下载量趋势数据
    const trendResponse = await statisticsService.getModelLibraryDown({
      modelLibraryId: selectedModel.value || 0, // 0表示所有模型
      startTime: startTime.toISOString(),
      endTime: endTime.toISOString()
    });

    const dates = [];
    const downloads = [];

    if (trendResponse.data && Array.isArray(trendResponse.data)) {
      trendResponse.data.forEach(item => {
        dates.push(item.x); // x是日期字符串
        downloads.push(item.y); // y是下载量数值
      });
    }

    trendData.value = { dates, downloads };

    // 更新趋势图表
    initTrendChart();
  } catch (error) {
    console.error("获取时间相关统计数据失败:", error);
    message.error("获取模型使用趋势数据失败，请稍后重试");

    // 使用默认数据
    trendData.value = {
      dates: ["今天"],
      downloads: [0]
    };
  }
}

// 获取静态统计数据（不依赖时间范围的数据）
async function fetchStaticData() {
  try {
    // 获取Top10下载量数据、分类统计数据和格式上传统计数据
    const [
      top10Response,
      categoryCountResponse,
      categoryDownResponse,
      formatUploadResponse
    ] = await Promise.all([
      statisticsService.getModelLibraryDownTop10(),
      statisticsService.getModelCategoryLibraryCount(),
      statisticsService.getModelCategoryDown(),
      statisticsService.getModelFormatUploadCount()
    ]);

    // 处理Top10数据
    const mockModelDetails = [];
    if (top10Response.data && Array.isArray(top10Response.data)) {
      top10Response.data.forEach((item, index) => {
        mockModelDetails.push({
          id: index + 1,
          name: item.x, // x是模型名称
          category: "未分类", // API没有返回分类信息，使用默认值
          downloadCount: item.y, // y是下载量
          formats: [
            { type: "glb", downloads: Math.floor(item.y * 0.6) },
            { type: "fbx", downloads: Math.floor(item.y * 0.4) }
          ]
        });
      });
    }

    // 设置模型详情数据
    modelDetails.value = mockModelDetails;

    // 合并分类数据
    const categoryMap = new Map();

    // 处理模型数量数据 (DataD格式: {x: 分类名称, y: 数量})
    if (
      categoryCountResponse.data &&
      Array.isArray(categoryCountResponse.data)
    ) {
      categoryCountResponse.data.forEach(item => {
        categoryMap.set(item.x, {
          category: item.x,
          modelCount: item.y || 0,
          downloadCount: 0
        });
      });
    }

    // 处理下载量数据 (DataD格式: {x: 分类名称, y: 下载量})
    if (categoryDownResponse.data && Array.isArray(categoryDownResponse.data)) {
      categoryDownResponse.data.forEach(item => {
        const existing = categoryMap.get(item.x);
        if (existing) {
          existing.downloadCount = item.y || 0;
        } else {
          categoryMap.set(item.x, {
            category: item.x,
            modelCount: 0,
            downloadCount: item.y || 0
          });
        }
      });
    }

    // 转换为数组
    categoryStats.value = Array.from(categoryMap.values());

    // 处理格式上传统计数据 (DataD格式: {x: 格式名称, y: 上传次数})
    if (formatUploadResponse.data && Array.isArray(formatUploadResponse.data)) {
      formatUploadStats.value = formatUploadResponse.data.map(item => ({
        format: item.x,
        uploadCount: item.y || 0
      }));
    } else {
      formatUploadStats.value = [];
    }

    // 从模型详情数据中汇总实际下载次数
    const totalDownloads = mockModelDetails.reduce((sum, model) => {
      return sum + (model.downloadCount || 0);
    }, 0);
    overview.downloadCount = totalDownloads;
    overview.uniqueUsers = Math.floor(overview.downloadCount * 0.6);

    // 更新相关图表
    updateRankingChart();
    updateDistributionChart();
    updateFormatChart();
  } catch (error) {
    console.error("获取静态统计数据失败:", error);
    message.error("获取静态统计数据失败，请稍后重试");

    // 使用默认数据
    overview.downloadCount = 0;
    overview.uniqueUsers = 0;
    categoryStats.value = [];
    formatUploadStats.value = [];
    modelDetails.value = [];
  }
}

async function fetchModelOptions() {
  try {
    // 获取模型库列表，用于筛选器
    const response = await libraryService.getList({
      pageIndex: 1,
      pageSize: 10000
    });
    if (response.data && Array.isArray(response.data)) {
      modelOptions.value = response.data.map(model => ({
        id: model.id,
        name: model.name
      }));
    }
  } catch (error) {
    console.error("获取模型列表失败:", error);
    modelOptions.value = [];
  }
}

onMounted(async () => {
  await fetchModelOptions();

  // 并行获取静态数据和时间相关数据
  await Promise.all([fetchStaticData(), fetchTimeBasedData()]);

  nextTick(() => {
    initTrendChart();
    initModelRankingChart();
    initCategoryDistributionChart();
    initFormatDistributionChart();
  });
});
</script>

<style lang="scss" scoped>
.filter-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 16px;

  .date-filter,
  .model-filter {
    display: flex;
    align-items: center;
    gap: 16px;
  }
}

.stat-card {
  background-color: #f9f9f9;
  border-radius: 8px;
  padding: 16px;
  display: flex;
  align-items: center;
  gap: 16px;
  height: 100px;

  .stat-icon {
    width: 56px;
    height: 56px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;

    .el-icon {
      font-size: 24px;
      color: white;
    }

    &.view-icon {
      background-color: #409eff;
    }

    &.download-icon {
      background-color: #67c23a;
    }

    &.active-icon {
      background-color: #f56c6c;
    }
  }

  .stat-content {
    .stat-value {
      font-size: 24px;
      font-weight: bold;
      line-height: 1.2;
    }

    .stat-label {
      font-size: 14px;
      color: #909399;
    }
  }
}

.chart-container {
  margin-top: 20px;

  .trend-chart {
    height: 350px;
    width: 100%;
  }
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;

  h3 {
    margin: 0;
  }
}

.model-ranking-chart,
.category-distribution-chart {
  height: 350px;
  width: 100%;
}

.model-info {
  display: flex;
  align-items: center;
  gap: 12px;

  .model-name {
    font-weight: 500;
  }

  .model-category {
    font-size: 12px;
    color: #909399;
  }
}

.model-option {
  display: flex;
  align-items: center;
  gap: 8px;
}

.mb-4 {
  margin-bottom: 16px;
}

.mt-4 {
  margin-top: 16px;
}

.format-distribution-card,
.popular-format-card {
  height: 450px;
  width: 100%;
}

.format-distribution-chart {
  height: 350px;
  width: 100%;
}
</style>
