<template>
  <div class="main">
    <el-form
      ref="formRef"
      :inline="true"
      :model="form"
      class="search-form bg-bg_color w-full pl-8 pt-[12px] overflow-auto"
    >
      <el-form-item label="名称" prop="name">
        <el-input
          v-model="form.name"
          placeholder="请输入名称"
          clearable
          class="w-[180px]!"
        />
      </el-form-item>
      <el-form-item label="分类" prop="modelCategoryId">
        <el-select
          v-model="form.modelCategoryId"
          placeholder="请选择模型分类"
          clearable
          style="width: 180px"
        >
          <el-option
            v-for="item in categories"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="标签" prop="tagKey">
        <el-input
          v-model="form.tagKey"
          placeholder="请输入标签关键词"
          clearable
          class="w-[180px]!"
        />
      </el-form-item>
      <el-form-item label="下载人" prop="downloader">
        <el-input
          v-model="form.downloader"
          placeholder="请输入下载人"
          clearable
          style="width: 180px"
        />
      </el-form-item>
      <el-form-item label="下载时间" prop="downloadTime">
        <el-date-picker
          v-model="form.downloadTime"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          class="w-[220px]!"
        />
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          :icon="useRenderIcon('ri:search-line')"
          :loading="loading"
          @click="onSearch"
        >
          搜索
        </el-button>
        <el-button @click="resetForm()"> 重置 </el-button>
      </el-form-item>
    </el-form>
    <PureTableBar title="调用日志" :columns="columns" @refresh="fetchLogs">
      <template v-slot="{ size, dynamicColumns }">
        <pure-table
          align-whole="center"
          showOverflowTooltip
          table-layout="auto"
          :loading="loading"
          :size="size"
          adaptive
          :adaptiveConfig="{ offsetBottom: 108 }"
          :data="logs"
          :columns="dynamicColumns"
          :pagination="{ ...pagination, size }"
          :header-cell-style="{
            background: 'var(--el-fill-color-light)',
            color: 'var(--el-text-color-primary)'
          }"
          @page-size-change="handleSizeChange"
          @page-current-change="handleCurrentChange"
        >
          <template #formats="{ row }">
            <el-tag class="mr-1 mb-1" type="success">
              {{ formatModelTypeToLabel(row.format) }}
            </el-tag>
          </template>
        </pure-table>
      </template>
    </PureTableBar>
  </div>
</template>

<script setup lang="ts">
import { PureTableBar } from "@/components/RePureTableBar";
import type { FormInstance } from "element-plus";
import { onMounted, reactive, ref } from "vue";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import FileModelService from "@/api/file";
import { Category } from "@/modules";
import categoryService from "@/api/category";
import { message } from "@/utils/message";
import { formatModelTypeToLabel } from "@/utils/fileUtils";
import dayjs from "dayjs";

defineOptions({
  name: "ModelLog"
});

const loading = ref(true);
const logs = ref([]);
const formRef = ref<FormInstance>();
const form = reactive({
  name: "",
  modelCategoryId: "",
  tagKey: "",
  downloader: "",
  downloadTime: null
});

const categories = ref<Category[]>([]);

const pagination = reactive({
  total: 0,
  pageSize: 10,
  currentPage: 1,
  background: true
});

const columns = [
  {
    label: "名称",
    prop: "name",
    minWidth: 120
  },
  {
    label: "分类",
    prop: "modelCategoryName",
    minWidth: 120
  },
  {
    label: "标签",
    prop: "tag",
    minWidth: 120
  },
  {
    label: "格式",
    prop: "formats",
    slot: "formats",
    minWidth: 120
  },
  {
    label: "LOD等级",
    prop: "lodLevel",
    minWidth: 120
  },
  {
    label: "下载人",
    prop: "downloader",
    minWidth: 100
  },
  {
    label: "下载时间",
    prop: "downloadTime",
    minWidth: 160
  }
] as any;

function handleSizeChange(val) {
  pagination.pageSize = val;
  fetchLogs();
}

function handleCurrentChange(val) {
  pagination.currentPage = val;
  fetchLogs();
}

function resetForm() {
  Object.assign(form, {
    name: "",
    category: "",
    tag: "",
    downloader: "",
    downloadTime: null
  });
  formRef.value?.resetFields();
  fetchLogs();
}

function onSearch() {
  pagination.currentPage = 1;
  fetchLogs();
}

const fetchCategories = async () => {
  try {
    const result = await categoryService.getList();
    categories.value = result.data || [];
  } catch (error) {
    console.error("获取分类列表失败:", error);
    message("获取分类列表失败", { type: "error" });
  }
};

// 模拟获取日志数据
async function fetchLogs() {
  loading.value = true;

  const res = await FileModelService.modelFileLog({
    pageSize: pagination.pageSize,
    pageIndex: pagination.currentPage,
    query: {
      name: form.name,
      modelCategoryId: form.modelCategoryId,
      tagKey: form.tagKey,
      downloader: form.downloader,
      startTime: form.downloadTime?.[0]
        ? dayjs(form.downloadTime[0]).startOf("day").toISOString()
        : undefined,
      endTime: form.downloadTime?.[1]
        ? dayjs(form.downloadTime[1]).endOf("day").toISOString()
        : undefined
    }
  });

  logs.value = res.data;
  pagination.total = res.totalCount;
  loading.value = false;
}

onMounted(() => {
  fetchCategories();
  fetchLogs();
});
</script>

<style lang="scss" scoped>
.search-form {
  margin-bottom: 16px;
  padding: 16px;
  border-radius: 4px;
}
</style>
