import type { ModelLibrary } from "@/modules";
import { http } from "@/utils/http";
import type { ListQ<PERSON>y, ListResult, Result } from "@/utils/models";

class LibraryService {
  private libraryUrl = "/api/modelLibrary";

  async getList(param: string | ListQuery): Promise<ListResult<ModelLibrary>> {
    const endpoint =
      typeof param === "string"
        ? `${this.libraryUrl}/getList`
        : this.libraryUrl;

    const params = typeof param === "string" ? { key: param } : param;

    return await http.get<ListQuery, ListResult<ModelLibrary>>(endpoint, {
      params
    });
  }

  async add(data: ModelLibrary) {
    const res = await http.post<ModelLibrary, Result<number>>(
      `${this.libraryUrl}`,
      {
        data
      }
    );
    return res;
  }

  async update(data: ModelLibrary) {
    const res = await http.put<ModelLibrary, ListResult<ModelLibrary>>(
      `${this.libraryUrl}`,
      data
    );
    return res;
  }

  async delete(id: number) {
    const res = await http.delete<Result<number>>(`${this.libraryUrl}`, id);
    return res;
  }
}

const libraryService = new LibraryService();
export default libraryService;
